<h1 class="mb-4">
  <strong class="text-2xl">Request for Proposals</strong>
  <span class="hidden md:inline md:before:pr-1 md:before:content-['—'] font-light"><%= @company.name %></span>
</h1>

<% if @request_for_proposals.any? %>
  <div class="space-y-6">
    <% @request_for_proposals.each do |request| %>
      <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
        <!-- Header with dates and locations -->
        <div class="mb-4 pb-4 border-b border-gray-100">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Pick Up</h3>
              <p class="text-gray-700">
                <span class="font-medium"><%= l(request.pickup_date, format: :mdy) %></span>
                <br>
                <span class="text-gray-600"><%= request.pickup_city.label %></span>
              </p>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">Drop Off</h3>
              <p class="text-gray-700">
                <span class="font-medium"><%= l(request.dropoff_date, format: :mdy) %></span>
                <br>
                <span class="text-gray-600"><%= request.dropoff_city.label %></span>
              </p>
            </div>
          </div>
        </div>

        <!-- Shipment details -->
        <div class="mb-4">
          <% if request.shipment_type.present? || request.specialized_service.present? || request.description.present? %>
            <h4 class="text-md font-semibold text-gray-900 mb-3">Shipment Details</h4>
            <div class="space-y-2">
              <% if request.shipment_type.present? %>
                <div class="flex">
                  <span class="font-medium text-gray-700 w-32 flex-shrink-0">Type:</span>
                  <span class="text-gray-600"><%= request.shipment_type.name %></span>
                </div>
              <% end %>
              <% if request.specialized_service.present? %>
                <div class="flex">
                  <span class="font-medium text-gray-700 w-32 flex-shrink-0">Service:</span>
                  <span class="text-gray-600"><%= request.specialized_service.name %></span>
                </div>
              <% end %>
              <% if request.description.present? %>
                <div class="flex">
                  <span class="font-medium text-gray-700 w-32 flex-shrink-0">Details:</span>
                  <span class="text-gray-600"><%= request.description %></span>
                </div>
              <% end %>
            </div>
          <% end %>
        </div>

        <!-- Contact information -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h4 class="text-md font-semibold text-gray-900 mb-3">Contact Information</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <div class="flex">
                <span class="font-medium text-gray-700 w-20 flex-shrink-0">Name:</span>
                <span class="text-gray-600"><%= request.name %></span>
              </div>
              <div class="flex">
                <span class="font-medium text-gray-700 w-20 flex-shrink-0">Company:</span>
                <span class="text-gray-600"><%= request.company_name %></span>
              </div>
              <% if request.industry.present? %>
                <div class="flex">
                  <span class="font-medium text-gray-700 w-20 flex-shrink-0">Industry:</span>
                  <span class="text-gray-600"><%= request.industry.name %></span>
                </div>
              <% end %>
            </div>
            <div class="space-y-2">
              <div class="flex">
                <span class="font-medium text-gray-700 w-16 flex-shrink-0">Email:</span>
                <a href="mailto:<%= request.email %>" class="text-blue-600 hover:text-blue-800 underline">
                  <%= request.email %>
                </a>
              </div>
              <div class="flex">
                <span class="font-medium text-gray-700 w-16 flex-shrink-0">Phone:</span>
                <a href="tel:<%= request.phone %>" class="text-blue-600 hover:text-blue-800 underline">
                  <%= request.phone %>
                </a>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer with timestamp -->
        <div class="mt-4 pt-4 border-t border-gray-100">
          <p class="text-sm text-gray-500">
            Received <%= time_ago_in_words(request.created_at) %> ago
          </p>
        </div>
      </div>
    <% end %>
  </div>

  <%= paginate @request_for_proposals %>
<% else %>
  <div class="text-center py-12">
    <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
      <%= svg_tag 'file-invoice-regular-full', class: 'h-12 w-12 text-gray-400' %>
    </div>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No requests yet</h3>
    <p class="text-gray-600 max-w-md mx-auto">
      When shippers and brokers submit requests for proposals to your company, they'll appear here.
    </p>
  </div>
<% end %>
