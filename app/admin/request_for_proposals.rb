ActiveAdmin.register RequestForProposal do
  menu parent: 'Companies'

  actions :all, except: %i(new create destroy)

  permit_params :notification_template

  filter :company_dot_number, as: :numeric

  scope :pending
  scope :notified

  member_action :notify, method: :post do
    resource.notify!
    RequestForProposalMailer.with(request: resource).submitted.deliver_later
    redirect_to admin_request_for_proposal_url(resource), notice: 'Queued email notification'
  end

  action_item :notify, only: :show, if: -> { resource.pending? } do
    link_to 'Notify', notify_admin_request_for_proposal_url(resource), method: :post, class: 'action-item-button'
  end

  member_action :mark_as_notified, method: :post do
    resource.notify!
    redirect_to admin_request_for_proposal_url(resource), notice: 'Marked as notified'
  end

  action_item :mark_as_notified, only: :show, if: -> { resource.pending? } do
    link_to 'Mark as Notified', mark_as_notified_admin_request_for_proposal_url(resource),
            method: :post, class: 'action-item-button'
  end

  member_action :preview, method: :get do
    @email = RequestForProposalMailer.with(request: resource).submitted
    response.content_type = 'text/html'
    part = @email.find_first_mime_type('text/html')
    render plain: part.decoded if params[:body] == 'true'
  end

  action_item :preview, only: :show, if: -> { resource.pending? } do
    link_to 'Preview Email', preview_admin_request_for_proposal_url(resource), class: 'action-item-button'
  end

  index pagination_total: false do
    selectable_column
    id_column
    column :company
    column :user
    column :email
    column :phone
    actions
  end

  form do |f|
    f.inputs do
      f.input :notification_template,
              as: :radio, collection: RequestForProposals::Entities.for(resource).template_name_options,
              input_html: { disabled: resource.notified? }
    end

    f.actions
  end

  controller do
    def scoped_collection
      end_of_association_chain.includes(:company, :user)
    end
  end
end
