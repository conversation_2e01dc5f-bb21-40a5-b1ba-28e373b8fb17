module Slack
  module ShipperIntent
    module Blocks
      TYPE_RENDERERS = {
        'carrier.search.performed' => SearchDetails
      }.freeze

      def self.render(analytics_company:, notification:, events:)
        renderers = [AnalyticsCompany.new(analytics_company:, notification:, events:)]
        events.group_by(&:visit_id).each_value do |visit_events|
          visit_events.group_by { |e| [e.type, e.scope] }.each do |(type, _scope), values|
            renderers << TYPE_RENDERERS.fetch(type, Signal).new(analytics_company:, notification:, events: values)
          end
          renderers << Visitor.new(analytics_company:, notification:, events: visit_events)
        end
        [Divider, Actions].each { |block| renderers << block.new(analytics_company:, notification:, events:) }
        renderers.flat_map(&:call)
      end
    end
  end
end
