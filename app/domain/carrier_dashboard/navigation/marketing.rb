module CarrierDashboard
  module Navigation
    class Marketing
      include Page

      self.icon = 'bullseye-pointer-regular'

      register('availability') { Availability }
      register('request_for_proposals') { RequestForProposals }
      register('sponsored_content') { SponsoredContent }
      register('email_signatures') { EmailSignature }
      register('badges') { Badges }
      register('widgets') { Widgets }

      def url
        Routes.carrier_dashboard_marketing_url(carrier)
      end
    end
  end
end
