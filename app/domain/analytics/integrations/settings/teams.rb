module Analytics
  module Integrations
    class Settings
      class Teams
        include StoreModel::Model

        attribute :access_token, :string
        attribute :refresh_token, :string
        attribute :token_type, :string
        attribute :expires_at, :datetime
        attribute :scope, :string

        def expired?
          expires_at.blank? || expires_at <= Time.zone.now
        end
      end
    end
  end
end
