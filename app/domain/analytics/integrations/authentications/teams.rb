module Analytics
  module Integrations
    module Authentications
      class Teams
        SCOPES = %w(ChannelMessage.Send ChannelMessage.ReadWrite Team.ReadBasic.All Channel.ReadBasic.All
                    offline_access).freeze

        attr_reader :integration

        delegate :provider, :settings, to: :integration
        delegate :teams, to: :settings
        delegate :refresh_token, to: :teams

        def initialize(integration)
          @integration = integration
        end

        def url
          Addressable::URI.new(
            scheme: 'https', host: 'login.microsoftonline.com', path: 'common/oauth2/v2.0/authorize',
            query: {
              client_id: CarrierSource::Credentials.lookup(:teams, :client_id),
              response_type: 'code',
              redirect_uri: redirect_uri,
              scope: SCOPES.join(' '),
              state: integration.to_param,
              response_mode: 'query'
            }.to_query
          ).to_s
        end

        def accept(code)
          ::Microsoft::Api::Oauth::Token.new(raise_errors: true).access(code:, redirect_uri:).then do |response|
            ::Analytics::Integrations::UpdateSettings.call(integration:, response:)
            ::Analytics::Integrations::Activate.queue_job(integration)
          end
          RedirectAction.default(integration)
        rescue ::Microsoft::Api::Error
          error
        end

        def error(...)
          RedirectAction.default(integration, alert: 'An error occurred while authenticating with Teams.')
        end

        def access_token
          refresh_access_token! if teams.expired?
          teams.access_token
        end

        def refresh_access_token!
          ::Microsoft::Api::Oauth::Token.new(raise_errors: true).refresh(refresh_token:).then do |response|
            attributes = SettingsFromResponse.extract(provider:, response:).slice(
              'access_token', 'refresh_token', 'expires_at'
            )
            integration.settings.teams.assign_attributes(attributes)
            integration.save!
          end
        end

        private

        def redirect_uri
          Routes.analytics_integrations_oauth_redirect_url
        end
      end
    end
  end
end
