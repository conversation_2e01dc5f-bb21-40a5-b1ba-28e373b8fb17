module BrokerageDashboard
  module Navigation
    class Marketing
      include Page

      self.icon = 'bullseye-pointer-regular'

      register('request_for_proposals') { RequestForProposals }
      register('sponsored_content') { SponsoredContent }
      register('email_signatures') { EmailSignature }
      register('widgets') { Widgets }
      register('onboarding_link') { OnboardingLink }

      def url
        Routes.brokerage_dashboard_marketing_url(brokerage)
      end
    end
  end
end
