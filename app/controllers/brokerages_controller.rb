class BrokeragesController < ApplicationController
  before_action :redirect_to_slug, only: %i(review show)
  after_action :track_utm_params, only: :show

  def claim
    if params[:query].present?
      query = Companies::PrefixQuery.call(params[:query])
      @brokerages = Companies::ElasticQuery.new(query, params, entity_types: :broker).records
    end

    render layout: 'core'
  end

  def show
    company = Company.broker.viewable.friendly.find(params[:id])
    @brokerage = company.as_entity(:broker)
    @profile = Brokerages::BrokerageInfo.new(@brokerage)
    @reviews = Brokerages::Reviews.call(@brokerage, params)
  end

  def review
    @company = Company.broker.viewable.friendly.find(params[:id])
    @brokerage = @company.as_entity(:broker)
    @profile = Brokerages::BrokerageInfo.new(@brokerage)

    if BrokerageReviewPolicy.new(current_user || User.new, BrokerageReview.new).new?
      return redirect_to new_brokerage_brokerage_review_url(@brokerage)
    end

    render layout: 'core'
  end

  private

  def redirect_to_slug
    brokerage = Company.friendly.find(params[:id])
    redirect_to brokerage_url(brokerage), status: :moved_permanently unless brokerage.to_param == params[:id]
  end

  def track_utm_params
    event_name = UtmParams::Track.call(params)
    return if event_name.blank?
    tracker.track event_name, analytics_global_properties.merge(id: @brokerage.id, entity_type: 'broker')
  end
end
