module CompanyDashboard
  module AssetsController
    extend ActiveSupport::Concern

    included do
      class_attribute :model_class

      def index
        @asset = @profile.assets.build
        render template: 'company_dashboard/assets/index'
      end

      def edit
        @asset = @profile.assets.find(params[:id])
        render template: 'company_dashboard/assets/edit'
      end

      def create
        @asset = @profile.assets.build(permitted_params)

        if @asset.save
          respond_to do |format|
            format.html { redirect_to url_for([@profile.entity, :dashboard, :assets]) }
            format.turbo_stream { render template: 'company_dashboard/assets/create' }
          end
        else
          head :unprocessable_content
        end
      end

      def update
        @asset = @profile.assets.find(params[:id])

        if @asset.update(permitted_params)
          respond_to do |format|
            format.html { redirect_to url_for([@profile.entity, :dashboard, :assets]) }
            format.turbo_stream { render template: 'company_dashboard/assets/update' }
          end
        else
          head :unprocessable_content
        end
      end

      def destroy
        @asset = @profile.assets.find(params[:id])

        if @asset.destroy
          respond_to do |format|
            format.html { redirect_to url_for([@profile.entity, :dashboard, :assets]) }
            format.turbo_stream { render template: 'company_dashboard/assets/destroy' }
          end
        else
          head :unprocessable_content
        end
      end

      private

      def permitted_params
        params.expect(model_class.model_name.param_key => %i(id file title description row_order_position))
      end
    end
  end
end
