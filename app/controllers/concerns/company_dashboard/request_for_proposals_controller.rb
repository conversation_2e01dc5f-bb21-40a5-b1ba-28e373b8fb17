module CompanyDashboard
  module RequestForProposalsController
    extend ActiveSupport::Concern

    included do
      def index
        @request_for_proposals =
          RequestForProposal.notified.where(company: @company, entity_type: @profile.entity.entity_type)
            .order(created_at: :desc)
            .page(params.fetch(:page, 1)).per(10)

        render template: 'company_dashboard/request_for_proposals/index'
      end
    end
  end
end
