FactoryBot.define do
  factory :analytics_integration, class: 'Analytics::Integration' do
    company

    trait :active do
      status { 'active' }
    end

    trait :carrier do
      entity_type { 'carrier' }
    end

    trait :broker do
      entity_type { 'broker' }
    end

    trait :hubspot do
      provider { 'hubspot' }

      settings do
        {
          hubspot: {
            token_type: 'bearer',
            refresh_token: '1e8fbfb1-8e96-4826-8b8d-c8af73715',
            access_token: 'CIrToaiiMhIHAAEAQAAAARiO1ooBIOP0sgEokuLtAEaOaTFnToZ3VjUbtl46MAAAAEAAAAAgAAAAAAAAAAAA',
            expires_in: 1800,
            expires_at: Time.zone.now.advance(seconds: 1800)
          }
        }
      end
    end

    trait :salesforce do
      provider { 'salesforce' }

      settings do
        {
          salesforce: {
            access_token: 'CIrToaiiMhIHAAEAQAAAARiO1ooBIOP0sgEokuLtAEaOaTFnToZ3VjUbtl46MAAAAEAAAAAgAAAAAAAAAAAA',
            refresh_token: '1e8fbfb1-8e96-4826-8b8d-c8af73715',
            signature: 'ppoIZQIBwP3ZPZ+CmKkC/9ZdLXmFMjldWPWYNaBGepQ=',
            scope: 'refresh_token sfap_api api',
            instance_url: 'https://na111.salesforce.com',
            id: 'https://test.salesforce.com/id/00D0o00000Dx3ZCEAZ/0050o00000Dx3ZCEAZ',
            token_type: 'Bearer',
            issued_at: '1617640000000',
            expires_at: Time.zone.now.advance(hours: 2)
          }
        }
      end
    end

    trait :slack do
      provider { 'slack' }

      settings do
        {
          slack: {
            access_token: '*****************************************************',
            token_type: 'bot',
            scope: 'chat:write,channels:read',
            bot_user_id: 'U0A2Y3Z4X',
            app_id: 'A0A2Y3Z4X'
          }
        }
      end
    end

    trait :teams do
      provider { 'teams' }

      settings do
        {
          teams: {
            access_token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IjVCM25SeHRRN2kwYm',
            refresh_token: 'OAQABAAAAAABnfiG-mA6NTae7CdWW7QfdAALIv8hC-pnJ-VLlsz8gvzGmT',
            token_type: 'Bearer',
            expires_at: Time.zone.now.advance(hours: 1),
            scope: 'ChannelMessage.Send'
          }
        }
      end
    end

    trait :webhook do
      provider { 'webhook' }
    end
  end
end
