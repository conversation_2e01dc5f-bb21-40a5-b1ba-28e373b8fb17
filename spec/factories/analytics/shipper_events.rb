FactoryBot.define do
  factory :analytics_shipper_event, class: 'Analytics::ShipperEvent' do
    transient do
      city { association :city }
    end

    time { Time.zone.now }
    type { 'carrier.search.performed' }
    event factory: %i(analytics_event)
    visit factory: %i(analytics_visit geolocated completed)
    analytics_company { visit.company }
    company factory: %i(company carrier)
    company_entity_type { 'carrier' }
    search_city_id { city.id }
    search_state_id { city.state.id }
    search_region_id { city.state.region.try(:id) }
    search_country_id { city.country.id }
    search_freight_ids { [] }
    search_truck_type_ids { [] }
    search_shipment_type_ids { [] }
    search_specialized_service_ids { [] }
  end
end
