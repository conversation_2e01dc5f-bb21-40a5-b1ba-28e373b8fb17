FactoryBot.define do
  factory :analytics_company, class: 'Analytics::Company' do
    name { Faker::Company.name }
    domain { Faker::Internet.unique.domain_name }
    employees_range { 51..200 }
    industry_id { ActiveRecord::FixtureSet.identify(:internet) }
    provider { 'snitcher' }

    trait :snitcher do
      provider { 'snitcher' }
    end

    trait :with_city do
      city
    end
  end
end
