FactoryBot.define do
  factory :analytics_company_event_feed_notification, class: 'Analytics::CompanyEventFeedNotification' do
    transient do
      company { association :company, :carrier }
    end

    feed { association :analytics_company_event_feed, :carrier, company: }

    trait :hubspot do
      notification_type { 'hubspot' }
      integration { association :analytics_integration, :active, :hubspot, :carrier, company: }
    end

    trait :salesforce do
      notification_type { 'salesforce' }
      integration { association :analytics_integration, :active, :salesforce, :carrier, company: }
    end

    trait :slack do
      notification_type { 'slack' }
      integration { association :analytics_integration, :active, :slack, :carrier, company: }
      properties do
        { slack: { channel: 'public' } }
      end
    end

    trait :teams do
      notification_type { 'teams' }
      integration { association :analytics_integration, :active, :teams, :carrier, company: }
      properties do
        {
          teams: {
            channel_id: '19:AK15fsy3UqeaU0KYSurDZ3n04mJr9puerOXT6cT3Gr01@thread.tacv2',
            team_id: 'e3868818-a450-45ed-8155-880f389d6a46'
          }
        }
      end
    end

    trait :webhook do
      notification_type { 'webhook' }
      integration { association :analytics_integration, :active, :webhook, :carrier, company: }
      properties do
        { webhook: { url: Faker::Internet.url } }
      end
    end
  end
end
