FactoryBot.define do
  factory :analytics_visit, class: 'Analytics::Visit' do
    visit_token { SecureRandom.uuid }
    visitor_token { SecureRandom.uuid }
    ip { Faker::Internet.ip_v4_address }
    started_at { Time.zone.now }

    trait :completed do
      company_status { 'completed' }
      company factory: :analytics_company
    end

    trait :geolocated do
      city { Faker::Address.city }
      region { Faker::Address.state }
      country { 'US' }
    end
  end
end
