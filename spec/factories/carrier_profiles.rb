FactoryBot.define do
  factory :carrier_profile do
    company factory: %i(company carrier with_city)
    bio { Faker::Lorem.paragraph }
    email { company.email }

    trait :with_assets do
      logo { Rack::Test::UploadedFile.new(Rails.root.join('spec/fixtures/files/logo.png'), 'image/png') }
      banner { Rack::Test::UploadedFile.new(Rails.root.join('spec/fixtures/files/logo.png'), 'image/png') }
    end
  end
end
