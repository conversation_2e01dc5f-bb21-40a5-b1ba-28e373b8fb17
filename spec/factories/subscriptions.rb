FactoryBot.define do
  factory :subscription do
    resource factory: :user
    external_id { "sub_#{Faker::Number.hexadecimal}" }
    status { 'active' }
    start_date { Time.zone.now }

    trait :brokerage do
      resource factory: :brokerage_profile
    end

    trait :carrier do
      resource factory: :carrier_profile
    end

    trait :broker do
      resource factory: :user
    end
  end
end
