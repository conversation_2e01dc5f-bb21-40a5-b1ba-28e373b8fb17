FactoryBot.define do
  factory :review do
    company
    user factory: %i(user broker)
    persona { user.persona }
    is_consider_next_time { true }
    consider_expensive { 1 }
    how_often { 1 }
    timeliness { 10 }
    communication { 10 }
    body { Faker::Lorem.paragraph(sentence_count: 5) }
    last_worked_with { { month: Time.zone.today.month, year: Time.zone.today.year } }
    freights { [ActiveRecord::FixtureSet.identify(:household)] }
    shipment_types { [ActiveRecord::FixtureSet.identify(:ftl)] }
    truck_types { [ActiveRecord::FixtureSet.identify(:flatbed)] }
    related_to_carrier { false }
    anonymous { false }
    offer_reference { false }
    submitted_at { Time.zone.now }
    star_rating { Faker::Number.between(from: 1, to: 5) }

    transient do
      origin { association :city }
      destination { association :city }
    end

    trait :pending do
      status { 'pending' }
    end

    trait :approved do
      status { 'approved' }
    end

    trait :rejected do
      status { 'rejected' }
      rejection_reason { 'LACK_DETAIL' }
    end

    trait :submitted do
      status { 'submitted' }
    end

    after :build do |review, e|
      review.review_lanes.build(pickup_city: e.origin, dropoff_city: e.destination)
    end
  end
end
