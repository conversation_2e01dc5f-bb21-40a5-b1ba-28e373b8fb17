FactoryBot.define do
  factory :insurance do
    operating_authority
    insurance_company_name { Faker::Company.name }
    bi_pd_max_limit { 750 }
    effective_date { Time.zone.today }
    policy_number { Faker::Number.number(digits: 6) }

    trait :bipd do
      insurance_type { 'BIPD' }
    end

    trait :cargo do
      insurance_type { 'CARGO' }
    end

    trait :bond do
      insurance_type { 'BOND' }
    end
  end
end
