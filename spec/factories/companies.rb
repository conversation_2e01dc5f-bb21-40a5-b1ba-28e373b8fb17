FactoryBot.define do
  factory :company do
    transient do
      postal_code { association :postal_code }
      common_authority { 'active' }
      docket_number { "MC#{Faker::Number.leading_zero_number(digits: 6)}" }
    end

    census
    city
    legal_name { Faker::Company.name }
    safety_rating { 'satisfactory' }
    carrier_operation { 'interstate' }
    city_name { city.try(:name) || Faker::Address.city }
    state_code { city.try(:state_code) || Faker::Address.state_abbr }
    country_code { city.try(:country_code) || 'US' }
    zip { postal_code.code }
    email { Faker::Internet.email }
    phone { Faker::PhoneNumber.phone_number }
    claim_token { SecureRandom.uuid }
    company_rep1 { Faker::Name.name }
    company_rep2 { Faker::Name.name }
    uuid { SecureRandom.uuid }

    trait :with_city do
      after(:create) do |c, e|
        CitiesPostalCode.create_with(city: c.city).find_or_create_by(postal_code: e.postal_code)
      end
    end

    trait :with_authority do
      after(:build, :stub) do |company, e|
        company.operating_authorities.build(
          attributes_for(:operating_authority, docket_number: e.docket_number, common_authority: e.common_authority)
        )
      end
    end

    trait :carrier do
      after(:build, :stub) do |company|
        company.entity_types.build(entity_type: 'carrier')
      end
    end

    trait :broker do
      after(:build, :stub) do |company|
        company.entity_types.build(entity_type: 'broker')
      end
    end
  end
end
