FactoryBot.define do
  factory :driver_carrier_review do
    company factory: %i(company carrier with_city)
    user factory: %i(user driver)
    nps { 10 }
    title { Faker::Lorem.characters(number: 50) }
    started_on { { month: Time.zone.today.month, year: Time.zone.today.year } }
    current_driver { true }
    employment_type { 'company_driver' }
    route_types { ['local'] }
    truck_types { [ActiveRecord::FixtureSet.identify(:flatbed)] }
    shipment_types { [ActiveRecord::FixtureSet.identify(:ftl)] }
    equipment { 7 }
    pay { 7 }
    home_time { 7 }
    training { 7 }
    management { 7 }
    benefits { 7 }
    safety { 7 }
    body { Faker::Lorem.characters(number: 75) }
    screenshot { Rack::Test::UploadedFile.new('spec/fixtures/files/logo.png', 'image/png') }

    trait :pending do
      status { 'pending' }
    end

    trait :submitted do
      status { 'submitted' }
    end

    trait :approved do
      status { 'approved' }
    end

    trait :rejected do
      status { 'rejected' }
      rejection_reason { 'LACK_DETAIL' }
    end
  end
end
