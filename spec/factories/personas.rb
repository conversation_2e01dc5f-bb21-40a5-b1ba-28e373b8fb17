FactoryBot.define do
  factory :persona do
    user
    city

    trait :broker do
      company
      type { 'broker' }
    end

    trait :carrier do
      company
      type { 'carrier' }
    end

    trait :shipper do
      type { 'shipper' }
    end

    trait :dispatcher do
      type { 'dispatcher' }
    end

    trait :seller do
      type { 'seller' }
    end

    trait :driver do
      type { 'driver' }
    end

    trait :verified do
      status { 'verified' }
    end
  end
end
