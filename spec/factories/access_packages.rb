FactoryBot.define do
  factory :access_package do
    active { true }

    trait :brokerage do
      resource factory: :brokerage_profile
      packages { %w(branding content_and_media shipper_intent_data shipper_intent_integration) }
    end

    trait :carrier do
      resource factory: :carrier_profile
      packages { %w(branding content_and_media shipper_intent_data shipper_intent_integration) }
    end

    trait :broker do
      resource factory: :user
      packages { %w(premium) }
    end
  end
end
