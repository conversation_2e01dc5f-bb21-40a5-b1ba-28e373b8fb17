FactoryBot.define do
  factory :brokerage_review do
    company factory: %i(company broker)
    nps { Faker::Number.between(from: 0, to: 10) }
    is_consider_next_time { true }
    body { Faker::Lorem.paragraph(sentence_count: 5) }
    last_worked_with { { month: Time.zone.today.month, year: Time.zone.today.year } }
    discovery { 'CarrierSource' }
    consider_expensive { Faker::Number.between(from: 1, to: 3) }
    how_often { Faker::Number.between(from: 1, to: 3) }
    communication { Faker::Number.between(from: 1, to: 10) }
    freights { [ActiveRecord::FixtureSet.identify(:household)] }
    shipment_types { [ActiveRecord::FixtureSet.identify(:ftl)] }
    electronic_tracking { false }
    offer_reference { false }
    related { false }
    anonymous { false }
    submitted_at { Time.zone.now }

    transient do
      origin { association :city }
      destination { association :city }
    end

    trait :carrier do
      user factory: %i(user carrier)
      persona { user.carrier }
      truck_types { [ActiveRecord::FixtureSet.identify(:flatbed)] }
      specialized_services { [ActiveRecord::FixtureSet.identify(:drop_trailer)] }
    end

    trait :shipper do
      user factory: %i(user shipper)
      persona { user.shipper }
      industry_id { ActiveRecord::FixtureSet.identify(:internet) }
    end

    trait :pending do
      status { 'pending' }
    end

    trait :submitted do
      status { 'submitted' }
    end

    trait :approved do
      status { 'approved' }
    end

    trait :rejected do
      status { 'rejected' }
      rejection_reason { 'LACK_DETAIL' }
    end

    after :build do |review, e|
      review.review_lanes.build(pickup_city: e.origin, dropoff_city: e.destination)
    end
  end
end
