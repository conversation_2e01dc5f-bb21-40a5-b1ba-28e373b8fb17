FactoryBot.define do
  factory :crash do
    report_number { "#{Faker::Address.state_abbr}#{Faker::Number.number(digits: 10)}" }
    date { Time.zone.today }
    dot_number { Faker::Number.number(digits: 5) }
    fatalities { Faker::Number.number(digits: 1) }
    injuries { Faker::Number.number(digits: 1) }
    tow_away { [true, false].sample }
    crash_id { Faker::Number.unique.number(digits: 5) }
  end
end
