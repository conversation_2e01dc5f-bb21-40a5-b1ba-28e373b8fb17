FactoryBot.define do
  factory :persona_verification do
    company { Faker::Company.name }
    title { Faker::Job.title }
    phone { Faker::PhoneNumber.phone_number }
    email { Faker::Internet.email }

    trait :broker do
      persona factory: %i(persona broker)
    end

    trait :shipper do
      persona factory: %i(persona shipper)
    end

    trait :pending do
      status { 'pending' }
    end

    trait :approved do
      status { 'approved' }
    end

    trait :rejected do
      status { 'rejected' }
    end
  end
end
