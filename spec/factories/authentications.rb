FactoryBot.define do
  factory :authentication do
    user
    email { Faker::Internet.email }
    provider { payload[:provider] }
    uid { payload[:uid] }

    trait :linkedin do
      payload { Faker::Omniauth.linkedin(email:) }
    end

    trait :facebook do
      payload { Faker::Omniauth.facebook(email:) }
    end

    trait :google do
      payload { Faker::Omniauth.google(email:).tap { |data| data[:provider] = 'google' } }
    end

    trait :twitter do
      payload { Faker::Omniauth.twitter(name: Faker::Name.name) }
    end
  end
end
