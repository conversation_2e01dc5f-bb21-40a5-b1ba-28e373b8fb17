FactoryBot.define do
  factory :request_for_proposal do
    company
    user
    company_name { Faker::Company.name }
    name { Faker::Name.name }
    email { Faker::Internet.email }
    phone { Faker::PhoneNumber.phone_number }
    description { Faker::Lorem.paragraph }
    pickup_date { Time.zone.today }
    pickup_city factory: :city
    dropoff_date { pickup_date + 1.day }
    dropoff_city factory: :city
    frequency { RequestForProposal::FREQUENCIES.without('other').sample }

    trait :carrier do
      entity_type { 'carrier' }
    end

    trait :broker do
      entity_type { 'broker' }
    end
  end
end
