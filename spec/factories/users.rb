FactoryBot.define do
  factory :user do
    first_name { Faker::Name.first_name }
    last_name { Faker::Name.last_name }
    email { Faker::Internet.email }
    password { Faker::Internet.password(special_characters: true) }
    verified { true }

    trait :broker do
      broker factory: %i(persona broker)
    end

    trait :carrier do
      carrier factory: %i(persona carrier)
    end

    trait :shipper do
      shipper factory: %i(persona shipper)
    end

    trait :dispatcher do
      dispatcher factory: %i(persona dispatcher)
    end

    trait :seller do
      seller factory: %i(persona seller)
    end

    trait :driver do
      driver factory: %i(persona driver)
    end
  end
end
