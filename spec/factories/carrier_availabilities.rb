FactoryBot.define do
  factory :carrier_availability do
    company
    origin_type { 'state' }
    origin_state_ids { %w(united-states:utah united-states:wyoming) }
    destination_type { 'state' }
    destination_state_ids { %w(united-states:colorado united-states:california) }
    truck_type_id { ActiveRecord::FixtureSet.identify(:flatbed) }
    date { Time.zone.today }
    length { Faker::Number.between(from: 1, to: 100) }
    weight { Faker::Number.between(from: 1, to: 100) }
  end
end
