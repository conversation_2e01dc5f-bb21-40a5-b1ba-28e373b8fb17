require 'spec_helper'

require 'simplecov'
require 'simplecov-cobertura'

SimpleCov.start do
  add_filter %w(/spec/)
  enable_coverage :branch
  primary_coverage :branch

  formatter(
    SimpleCov::Formatter::MultiFormatter.new(
      [SimpleCov::Formatter::CoberturaFormatter, SimpleCov::Formatter::HTMLFormatter]
    )
  )
end

ENV['RAILS_ENV'] ||= 'test'
require_relative '../config/environment'
# Prevent database truncation if the environment is production
abort('The Rails environment is running in production mode!') if Rails.env.production?
require 'rspec/rails'

require 'active_support'
require 'active_support/testing/time_helpers'
require 'capybara/rspec'
require 'clearance/rspec'
require 'pry-rails'
require 'sidekiq/testing'
require 'view_component/test_helpers'
require 'webmock/rspec'
require 'with_model'

require_relative 'support/sidekiq/test_batch'

begin
  ActiveRecord::Migration.maintain_test_schema!
rescue ActiveRecord::PendingMigrationError => e
  puts e.to_s.strip
  exit 1
end

WebMock.disable_net_connect!(allow_localhost: true, allow: 'api.stripe.com', net_http_connect_on_start: true)

OmniAuth.config.test_mode = true

Shoulda::Matchers.configure do |config|
  config.integrate do |with|
    with.test_framework :rspec
    with.library :rails
  end
end

RSpec.configure do |config|
  config.extend WithModel

  config.include FactoryBot::Syntax::Methods
  config.include ActiveSupport::Testing::TimeHelpers

  config.include ViewComponent::TestHelpers, type: :component
  config.include Capybara::RSpecMatchers, type: :component
  config.include Clearance::Testing::ViewHelpers, type: :component

  config.filter_rails_from_backtrace!
  config.infer_spec_type_from_file_location!
  config.use_transactional_fixtures = true
  config.fixture_paths = [Rails.root.join('spec/fixtures').to_s]
  config.global_fixtures = :all

  # Global fixture class mappings
  config.include(Module.new do
    def self.included(base)
      return unless base.respond_to?(:set_fixture_class)
      base.set_fixture_class analytics_industries: Analytics::Industry
    end
  end)

  config.before(:suite) do
    ApplicationRecord.connection.tap do |conn|
      conn.select_values('select matviewname from pg_matviews order by matviewname').each do |view|
        conn.execute "refresh materialized view #{view}"
      end
    end

    Elastic::PutIndexTemplate.call(Analytics::ShipperEvent)

    %w(analytics_events analytics_shipper_events analytics_aggregate_shipper_events analytics_visits).each do |table|
      Analytics::PartitionByMonth.premake(table:, months: 1)
    end
  end

  config.before(:each, type: :component) do
    vc_test_controller.extend Clearance::Testing::ViewHelpers::CurrentUser
  end

  config.before(:each, :jsonapi, type: :request) do
    host! 'api.carriersource.io'
  end

  config.around(:each, :jsonapi, type: :request) do |example|
    original_url_options = Rails.application.routes.default_url_options
    Rails.application.routes.default_url_options = { host: 'api.carriersource.io', protocol: 'https' }
    begin
      example.run
    ensure
      Rails.application.routes.default_url_options = original_url_options
    end
  end

  config.after(:each, :jsonapi, type: :request) do |example|
    next if response.body.blank?
    example.metadata[:response][:schema][:example] = JSON.parse(response.body, symbolize_names: true)
  end

  config.before { CarrierSource.redis.call('FLUSHDB') }
  config.before { Faker::UniqueGenerator.clear }
  config.before { Records.each_key { |key| Records.resolve(key).reset } }
  config.before { Singleton.__init__(ActiveCampaign::CustomAccountFields) }
  config.before { Singleton.__init__(Carriers::TopRated) }
  config.before { Singleton.__init__(TruckingCompanies::Loads::Featured) }
  config.before { ActiveSupport::CurrentAttributes.clear_all }

  config.before do |example|
    Wisper.clear
    ActiveSupport.run_load_hooks('carrier_source/wisper') if example.metadata[:wisper]
  end

  config.before do
    Sidekiq::Job.clear_all
    Sidekiq::TestBatch.clear
    ActiveJob::Base.queue_adapter.enqueued_jobs.clear
  end

  config.before :each, :elasticsearch do
    Elasticsearch::API::Utils.__rescue_from_not_found do
      Analytics::ShipperEvent.es.client.indices.perform_request(
        'DELETE', "_data_stream/#{Analytics::ShipperEvent.index_name}"
      )
    end

    Elasticsearch::Model.client.indices.delete(index: '*_test*')
  end

  config.before :each, type: proc { |v| %i(request system).include?(v) } do
    stub_request(:get, 'https://www.cloudflare.com/ips-v4/')
      .and_return(status: 200, body: Rails.root.join('spec/fixtures/cloudflare/ips-v4.txt').read,
                  headers: { 'Content-Type' => 'text/plain;charset=UTF-8' })

    stub_request(:get, 'https://www.cloudflare.com/ips-v6/')
      .and_return(status: 200, body: Rails.root.join('spec/fixtures/cloudflare/ips-v6.txt').read,
                  headers: { 'Content-Type' => 'text/plain;charset=UTF-8' })
  end

  config.before do
    stub_request(:get, %r{https://api.stripe.com/v1/prices})
      .and_return(status: 200, body: Rails.root.join('spec/fixtures/stripe/prices/list.json').read,
                  headers: { 'Content-Type' => 'application/json' })
  end

  config.after do
    FileUtils.rm_rf(ActiveStorage::Blob.service.root)
  end

  config.around(:each, :allow_forgery_protection) do |example|
    original_forgery_protection = ActionController::Base.allow_forgery_protection
    ActionController::Base.allow_forgery_protection = true
    begin
      example.run
    ensure
      ActionController::Base.allow_forgery_protection = original_forgery_protection
    end
  end

  config.before do
    Audited.auditing_enabled = false
  end

  config.before(:each, :audited) do
    Audited.auditing_enabled = true
  end
end
