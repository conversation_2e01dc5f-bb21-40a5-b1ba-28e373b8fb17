require 'rails_helper'

RSpec.describe Widgets::CollectReviewComponent, type: :component do
  context 'when style is not provided' do
    subject(:component) { described_class.new }

    it 'renders' do
      render_inline(component)
      expect(page).to have_css '.btn.hollow.primary'
    end
  end

  context 'when style is provided' do
    subject(:component) { described_class.new(style: 'gradient') }

    it 'renders' do
      render_inline(component)
      expect(page).to have_css '.btn.hollow.gradient'
    end
  end
end
