require 'rails_helper'

RSpec.describe Widgets::ReadReviewsComponent, type: :component do
  context 'when style is not provided' do
    subject(:component) { described_class.new(review_count: 5) }

    it 'defaults to square_logo_primary' do
      render_inline(component)
      expect(page).to have_css '.bg-primary'
    end
  end

  context 'when style is provided' do
    subject(:component) { described_class.new(review_count: 5, style: 'square_logo_gradient') }

    it 'uses the provided style' do
      render_inline(component)
      expect(page).to have_css '.bg-linear-45.from-primary.to-purple'
    end
  end
end
