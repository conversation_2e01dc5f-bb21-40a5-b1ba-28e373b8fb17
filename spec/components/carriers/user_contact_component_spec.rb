require 'rails_helper'

RSpec.describe Carriers::UserContactComponent, type: :component do
  subject(:component) { described_class.new(carrier:) }

  let(:carrier_profile) { create :carrier_profile, company: }
  let(:company) { create :company, slug: 'ez-transport' }
  let(:carrier) { carrier_profile.carrier }

  before do
    carrier.profile.contacts.create(type: 'owner', name: '<PERSON>', email: '<PERSON>', phone: '8006001234')
  end

  it 'renders link to contact information' do
    render_inline component
    expect(page).to have_link('Contact', href: vc_test_controller.carrier_contact_url(carrier))
  end
end
