require 'rails_helper'

RSpec.describe Carriers::SaferLinkComponent, type: :component do
  subject(:component) { described_class.new(carrier:, user:) }

  let(:census) { create :census, dot_number: 215 }
  let(:carrier) { create :company, census: }
  let(:user) { create :user }

  before do
    user.personas.create(type: 'shipper', status: 'verified')
  end

  describe '#safer_url' do
    it 'returns the safer url' do
      render_inline component
      expect(page).to have_link 'SAFER Listing', href: 'https://safer.fmcsa.dot.gov/query.asp?query_param=USDOT&query_string=215&query_type=queryCarrierSnapshot&searchtype=ANY'
    end
  end
end
