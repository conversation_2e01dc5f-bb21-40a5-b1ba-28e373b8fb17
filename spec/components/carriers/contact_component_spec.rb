require 'rails_helper'

RSpec.describe Carriers::ContactComponent, type: :component do
  subject(:component) { described_class.new(carrier:, user:) }

  let(:carrier_profile) { create :carrier_profile, company: }
  let(:company) { create :company, slug: 'ez-transport' }
  let(:carrier) { carrier_profile.carrier }
  let(:user) { build_stubbed :user }

  before do
    carrier.profile.contacts.create(type: 'owner', name: '<PERSON>', email: '<EMAIL>', phone: '8006001234')
  end

  context 'when carrier is not active' do
    let(:company) { create :company, dot_number: Census.maximum(:dot_number).to_i.next }

    it 'does not render' do
      render_inline(component)
      expect(page).to have_no_content 'Contact'
    end
  end

  context 'when user is verified' do
    context 'when user is broker' do
      before do
        user.personas.build(type: 'broker', status: 'verified', company:)
      end

      it 'renders link to contact information' do
        render_inline(component)
        expect(page).to have_link 'Contact', href: vc_test_controller.carrier_contact_url(carrier)
      end
    end

    context 'when user is dispatcher' do
      before do
        user.personas.build(type: 'dispatcher', status: 'verified', company:)
      end

      it 'does not render link to contact information' do
        render_inline(component)
        expect(page).to have_no_link 'Contact', href: vc_test_controller.carrier_contact_url(carrier)
      end
    end
  end

  context 'when user is not verified' do
    before do
      user.personas.build(type: 'broker', status: 'pending', company:)
    end

    it 'does not render link to contact information' do
      render_inline(component)
      expect(page).to have_no_link 'Contact', href: vc_test_controller.carrier_contact_url(carrier)
    end
  end

  context 'when user is blank' do
    let(:user) { nil }

    it 'does not render link to contact information' do
      render_inline(component)
      expect(page).to have_no_link 'Contact', href: vc_test_controller.carrier_contact_url(carrier)
    end
  end
end
