require 'rails_helper'

RSpec.describe BrokeragesController do
  describe 'GET claim', :elasticsearch do
    let(:brokerage) { create :company, :broker, :with_city }
    let!(:brokerage_profile) { create :brokerage_profile, company: brokerage }

    before do
      Brokerage.es.import force: true, refresh: true
    end

    context 'with no query' do
      it 'responds with ok status' do
        get claim_brokerages_url
        expect(response).to have_http_status :ok
      end
    end

    it 'responds with ok status' do
      get claim_brokerages_url(query: brokerage.legal_name)
      expect(response).to have_http_status :ok
    end
  end

  describe 'GET show' do
    let(:brokerage) { create :company, :broker, :with_city, :with_authority, legal_name: 'Cooler Logistics LLC' }

    before do
      create :brokerage_profile, company: brokerage
    end

    it 'responds with ok status' do
      get brokerage_url(brokerage)
      expect(response).to have_http_status :ok
    end

    context 'when brokerage slug is stale' do
      before do
        brokerage.update(legal_name: 'Cooler Logistics')
      end

      it 'redirects to current slug' do
        get '/brokerages/cooler-logistics-llc'
        expect(response).to redirect_to '/brokerages/cooler-logistics'
        expect(response).to have_http_status :moved_permanently
      end
    end

    context 'with utm params' do
      it 'tracks utm params' do
        get brokerage_url(brokerage, utm_source: 'email_signature', utm_campaign: brokerage.to_param),
            headers: { 'HTTP_USER_AGENT' => Faker::Internet.user_agent }
        expect(Analytics::Event.last).to have_attributes name: 'Email Signature Clicked'
      end
    end
  end

  describe 'GET review' do
    let(:company) { create :company, :broker, :with_city, :with_authority }

    context 'with logged in user' do
      let(:user) { create :user, :carrier }

      it 'redirects to review page' do
        get review_brokerage_url(company, as: user.to_param)
        expect(response).to redirect_to new_brokerage_brokerage_review_url(company)
      end
    end

    context 'with logged out user' do
      it 'responds with ok status' do
        get review_brokerage_url(company)
        expect(response).to have_http_status :ok
      end
    end
  end
end
