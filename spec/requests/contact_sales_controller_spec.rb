require 'rails_helper'

RSpec.describe ContactSalesController do
  describe 'GET new' do
    it 'responds with ok status' do
      get new_contact_sales_url
      expect(response).to have_http_status :ok
    end
  end

  describe 'POST create' do
    it 'redirects to root' do
      post contact_sales_url, params: { first_name: '<PERSON>', last_name: '<PERSON>' }
      expect(response).to redirect_to root_url
    end

    it 'queues mailer' do
      post contact_sales_url, params: { first_name: '<PERSON>', last_name: '<PERSON>' }
      expect(ActionMailer::MailDeliveryJob).to have_been_enqueued.with('ContactSalesMailer', 'contact', any_args)
    end
  end
end
