require 'rails_helper'

RSpec.describe PricingsController do
  let(:user) { create(:user, persona) }

  describe 'GET #show' do
    context 'when user is not present' do
      it 'redirects to carrier pricing page' do
        get pricing_url
        expect(response).to redirect_to carrier_pricing_url
      end
    end

    context 'when user is present' do
      let(:persona) { :shipper }

      it 'redirects to correct persona' do
        get pricing_url(as: user.to_param)
        expect(response).to redirect_to broker_pricing_url
      end
    end
  end

  describe 'GET #carrier' do
    context 'when user is not present' do
      it 'returns success status' do
        get carrier_pricing_url
        expect(response).to have_http_status :ok
      end
    end

    context 'when user is present' do
      let(:persona) { :carrier }

      before do
        create :carrier_profile_user, :verified, user:
      end

      it 'returns success status' do
        get carrier_pricing_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET #brokerage' do
    context 'when user is not present' do
      it 'returns success status' do
        get brokerage_pricing_url
        expect(response).to have_http_status :ok
      end
    end

    context 'when user is present' do
      let(:persona) { :broker }

      before do
        create :brokerage_profile_user, :verified, user:
      end

      it 'returns success status' do
        get brokerage_pricing_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET #broker' do
    context 'when user is not present' do
      it 'returns success status' do
        get broker_pricing_url
        expect(response).to have_http_status :ok
      end
    end

    context 'when user is present' do
      let(:persona) { :broker }

      it 'returns success status' do
        get broker_pricing_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET #dispatcher' do
    context 'when user is not present' do
      it 'returns success status' do
        get dispatcher_pricing_url
        expect(response).to have_http_status :ok
      end
    end

    context 'when user is present' do
      let(:persona) { :dispatcher }

      it 'returns success status' do
        get dispatcher_pricing_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end
end
