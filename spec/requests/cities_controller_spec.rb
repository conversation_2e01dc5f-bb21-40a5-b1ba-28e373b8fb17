require 'rails_helper'

RSpec.describe CitiesController, :elasticsearch do
  describe 'GET search' do
    let(:city) { create :city, name: 'Salt Lake City', state_code: 'UT' }
    let(:postal_code) { create :postal_code, code: '84108' }

    before do
      create(:cities_postal_code, city:, postal_code:)
      City.es.import force: true, refresh: true
    end

    it 'renders success' do
      get search_cities_url(query: 'salt', format: 'json')
      expect(response).to have_http_status :ok
    end

    it 'returns json data' do
      get search_cities_url(query: 'salt', format: 'json')

      expect(JSON.parse(response.body)).to(
        contain_exactly(
          hash_including(
            'name' => 'Salt Lake City', 'state_code' => 'UT', 'id' => city.id,
            'full_slug' => 'united-states:utah:salt-lake-city', 'zipcodes' => ['84108'],
            'url' => match('/trucking-companies/united-states/utah/salt-lake-city')
          )
        )
      )
    end
  end
end
