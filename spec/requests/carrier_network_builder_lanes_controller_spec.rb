require 'rails_helper'

RSpec.describe CarrierNetworkBuilderLanesController do
  let(:user) { create :user, :shipper }
  let(:carrier_network_builder) { create :carrier_network_builder, user: }
  let(:lane) { create :carrier_network_builder_lane, carrier_network_builder: }

  shared_examples 'a protected action' do |method|
    context 'when user not logged in' do
      it 'redirects to login' do
        public_send method, url
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is logged in' do
      context 'when user is not authorized' do
        let(:user) { create :user, :shipper }

        it 'redirects back to root' do
          public_send method, Functions::AppendToUrl.call(url, as: user.to_param)
          expect(response).to redirect_to root_url
        end
      end
    end
  end

  describe 'GET show' do
    let(:url) { carrier_network_builder_lane_path(carrier_network_builder.uuid, lane) }

    it_behaves_like 'a protected action', :get

    context 'when user is authorized' do
      let(:user) { create :user, :shipper, admin: true }
      let(:url) { carrier_network_builder_lane_path(carrier_network_builder.uuid, lane, as: user.to_param) }

      it 'renders the lane show page' do
        get url, headers: { 'Accept' => 'text/vnd.turbo-stream.html' }
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET new' do
    let(:url) { new_carrier_network_builder_lane_path(carrier_network_builder.uuid) }

    it_behaves_like 'a protected action', :get

    context 'when user is authorized' do
      let(:user) { create :user, :shipper, admin: true }
      let(:url) { new_carrier_network_builder_lane_path(carrier_network_builder.uuid, as: user.to_param) }

      it 'renders the new lane page' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { carrier_network_builder_lanes_path(carrier_network_builder.uuid) }

    let(:valid_params) do
      {
        carrier_network_builder_lane: {
          origin_id: 'united-states:illinois:chicago',
          destination_id: 'united-states:california:los-angeles',
          volume: '10-15 loads/week',
          frequency: 'Weekly'
        }
      }
    end

    it_behaves_like 'a protected action', :post

    context 'when user is authorized' do
      let(:user) { create :user, :shipper, admin: true }
      let(:url) { carrier_network_builder_lanes_path(carrier_network_builder.uuid, as: user.to_param) }

      it 'creates a new lane' do
        expect { post url, params: valid_params }.to change(CarrierNetworkBuilderLane, :count).by(1)
        expect(response).to redirect_to carrier_network_builder_lanes_path(carrier_network_builder.uuid)
      end

      context 'when create fails' do
        before do
          allow_any_instance_of(CarrierNetworkBuilderLane).to receive(:save).and_return(false)
        end

        it 'renders the new lane page' do
          post url, params: valid_params
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'GET edit' do
    let(:url) { edit_carrier_network_builder_lane_path(carrier_network_builder.uuid, lane) }

    it_behaves_like 'a protected action', :get

    context 'when user is authorized' do
      let(:user) { create :user, :shipper, admin: true }
      let(:url) { edit_carrier_network_builder_lane_path(carrier_network_builder.uuid, lane, as: user.to_param) }

      it 'renders the edit lane page' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { carrier_network_builder_lane_path(carrier_network_builder.uuid, lane) }

    it_behaves_like 'a protected action', :patch

    context 'when user is authorized' do
      let(:user) { create :user, :shipper, admin: true }
      let(:url) { carrier_network_builder_lane_path(carrier_network_builder.uuid, lane, as: user.to_param) }
      let(:update_params) { { carrier_network_builder_lane: { volume: '20-25 loads/week' } } }

      it 'updates the lane' do
        patch url, params: update_params
        expect(response).to redirect_to carrier_network_builder_lanes_path(carrier_network_builder.uuid)
        expect(lane.reload.volume).to eq('20-25 loads/week')
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(CarrierNetworkBuilderLane).to receive(:update).and_return(false)
        end

        it 'renders the edit lane page' do
          patch url, params: update_params
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'DELETE destroy' do
    let(:url) { carrier_network_builder_lane_path(carrier_network_builder.uuid, lane) }

    it_behaves_like 'a protected action', :delete

    context 'when user is authorized' do
      let(:user) { create :user, :shipper, admin: true }
      let(:url) { carrier_network_builder_lane_path(carrier_network_builder.uuid, lane, as: user.to_param) }

      it 'deletes the lane' do
        lane
        expect { delete url }.to change(CarrierNetworkBuilderLane, :count).by(-1)
        expect(response).to redirect_to carrier_network_builder_lanes_path(carrier_network_builder.uuid)
      end
    end
  end
end
