require 'rails_helper'

RSpec.describe ProtectedFeaturesController do
  describe 'GET show' do
    context 'when feature is missing' do
      it 'responds with bad argument' do
        get protected_feature_url
        expect(response).to have_http_status :bad_request
      end
    end

    context 'when feature is invalid' do
      it 'uses the default feature' do
        get protected_feature_url(feature: 'invalid')
        expect(response.body).to include 'use this premium feature'
        expect(response.body).to include 'you must be a premium subscriber'
      end
    end

    context 'when tier is missing' do
      it 'assigns the premium tier' do
        get protected_feature_url(feature: 'contacts')
        expect(response.body).to include 'view contact information'
        expect(response.body).to include 'you must be a premium subscriber'
      end

      it 'responds with ok status' do
        get protected_feature_url(feature: 'contacts')
        expect(response).to have_http_status :ok
      end
    end

    context 'when tier is login' do
      it 'assigns the login tier' do
        get protected_feature_url(feature: 'contacts', tier: 'login')
        expect(response.body).to include 'view contact information'
        expect(response.body).to include 'you must be logged in'
      end

      it 'responds with ok status' do
        get protected_feature_url(feature: 'contacts', tier: 'login')
        expect(response).to have_http_status :ok
      end
    end

    context 'when tier is premium' do
      it 'assigns the premium tier' do
        get protected_feature_url(feature: 'contacts', tier: 'premium')
        expect(response.body).to include 'view contact information'
        expect(response.body).to include 'you must be a premium subscriber'
      end

      it 'responds with ok status' do
        get protected_feature_url(feature: 'contacts', tier: 'premium')
        expect(response).to have_http_status :ok
      end
    end

    context 'when tier is verification' do
      let(:user) { create :user, :broker }

      it 'assigns the verification tier' do
        get protected_feature_url(feature: 'contacts', tier: 'verification', as: user.to_param)
        expect(response.body).to include 'view contact information'
        expect(response.body).to include '<strong>verified</strong> broker or shipper'
      end

      it 'responds with ok status' do
        get protected_feature_url(feature: 'contacts', tier: 'verification', as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end
end
