require 'rails_helper'
require_relative 'shared_examples'
require_relative '../carrier_dashboard/shared_contexts'

RSpec.describe Reviews::WidgetsController do
  include_context 'with verified carrier profile user'

  let(:review) { create :review, :approved, company: }
  let!(:widget) { create :widget, :carrier, company:, widget_type: 'review' }

  describe 'PATCH update' do
    let(:url) { review_widget_url(review) }

    it_behaves_like 'a protected carrier review controller action', :patch

    context 'when carrier user is authorized' do
      let(:url) { review_widget_url(review, as: user.to_param) }

      it 'redirects to review' do
        patch url
        expect(response).to redirect_to review_url(review)
        expect(widget.reviews).to include review
      end

      context 'when update fails' do
        before do
          allow(Reviews::AddToWidget).to receive(:call).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url
          expect(response).to have_http_status :unprocessable_content
          expect(widget.reviews).not_to include review
        end
      end
    end
  end

  describe 'DELETE destroy' do
    let(:url) { review_widget_url(review) }

    it_behaves_like 'a protected carrier review controller action', :delete

    context 'when carrier user is authorized' do
      let(:url) { review_widget_url(review, as: user.to_param) }

      before do
        WidgetReview.create!(review:, widget:)
      end

      it 'redirects to review' do
        delete url
        expect(response).to redirect_to review_url(review)
        expect(widget.reviews).not_to include review
      end

      context 'when delete fails' do
        before do
          allow(Reviews::RemoveFromWidget).to receive(:call).and_return(false)
        end

        it 'responds with unprocessable status' do
          delete url
          expect(response).to have_http_status :unprocessable_content
          expect(widget.reviews).to include review
        end
      end
    end
  end
end
