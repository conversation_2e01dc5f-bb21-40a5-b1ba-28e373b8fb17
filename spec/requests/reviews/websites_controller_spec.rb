require 'rails_helper'
require_relative 'shared_examples'
require_relative '../carrier_dashboard/shared_contexts'

RSpec.describe Reviews::WebsitesController do
  include_context 'with verified carrier profile user'

  let(:review) { create :review, :approved, company: }
  let!(:website) { create :carrier_profile_website, carrier_profile: }

  describe 'PATCH update' do
    let(:url) { review_website_url(review) }

    it_behaves_like 'a protected carrier review controller action', :patch

    context 'when carrier user is authorized' do
      let(:url) { review_website_url(review, as: user.to_param) }

      it 'redirects to review' do
        patch url
        expect(response).to redirect_to review_url(review)
        expect(website.reviews).to include review
      end

      context 'when update fails' do
        before do
          allow(Reviews::AddToWebsite).to receive(:call).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url
          expect(response).to have_http_status :unprocessable_content
          expect(website.reviews).not_to include review
        end
      end
    end
  end

  describe 'DELETE destroy' do
    let(:url) { review_website_url(review) }

    it_behaves_like 'a protected carrier review controller action', :delete

    context 'when carrier user is authorized' do
      let(:url) { review_website_url(review, as: user.to_param) }

      before do
        CarrierProfileWebsiteReview.create!(review:, carrier_profile_website: website)
      end

      it 'redirects to review' do
        delete url
        expect(response).to redirect_to review_url(review)
        expect(website.reviews).not_to include review
      end

      context 'when delete fails' do
        before do
          allow(Reviews::RemoveFromWebsite).to receive(:call).and_return(false)
        end

        it 'responds with unprocessable status' do
          delete url
          expect(response).to have_http_status :unprocessable_content
          expect(website.reviews).to include review
        end
      end
    end
  end
end
