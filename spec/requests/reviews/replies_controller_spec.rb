require 'rails_helper'
require_relative 'shared_examples'
require_relative '../carrier_dashboard/shared_contexts'

RSpec.describe Reviews::RepliesController do
  include_context 'with verified carrier profile user'

  let(:review) { create :review, :approved, company: }

  describe 'GET new' do
    let(:url) { new_review_reply_url(review) }

    it_behaves_like 'a protected carrier review controller action', :get

    context 'when carrier user is authorized' do
      let(:url) { new_review_reply_url(review, as: user.to_param) }

      it 'responds with ok status' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { review_reply_url(review) }

    it_behaves_like 'a protected carrier review controller action', :post

    context 'when carrier user is authorized' do
      let(:url) { review_reply_url(review, as: user.to_param) }

      it 'redirects to show' do
        post url, params: { reply: { comment: 'Thanks for your feedback' } }
        expect(response).to redirect_to review_reply_url(review)
        expect(review.reload.reply).to have_attributes comment: 'Thanks for your feedback', user:
      end

      context 'when create fails' do
        before do
          allow_any_instance_of(Reply).to receive(:save).and_return(false)
        end

        it 'responds with unprocessable status' do
          post url, params: { reply: { comment: 'Thanks for your feedback' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'GET show' do
    let(:url) { review_reply_url(review) }

    it 'responds with ok status' do
      get url
      expect(response).to have_http_status :ok
    end
  end

  describe 'GET edit' do
    let(:url) { edit_review_reply_url(review) }

    it_behaves_like 'a protected carrier review controller action', :get

    context 'when carrier user is authorized' do
      let(:url) { edit_review_reply_url(review, as: user.to_param) }

      before do
        review.create_reply(user:, comment: 'We are sorry')
      end

      it 'responds with ok status' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { review_reply_url(review) }

    before do
      review.create_reply(user:, comment: 'We are sorry')
    end

    it_behaves_like 'a protected carrier review controller action', :patch

    context 'when carrier user is authorized' do
      let(:url) { review_reply_url(review, as: user.to_param) }

      it 'redirects to show' do
        patch url, params: { reply: { comment: 'Thanks for your feedback' } }
        expect(response).to redirect_to review_reply_url(review)
        expect(review.reload.reply).to have_attributes comment: 'Thanks for your feedback', user:
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(Reply).to receive(:save).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url, params: { reply: { comment: 'Thanks for your feedback' } }
          expect(response).to have_http_status :unprocessable_content
          expect(review.reload.reply).to have_attributes comment: 'We are sorry', user:
        end
      end
    end
  end

  describe 'DELETE destroy' do
    let(:url) { review_reply_url(review) }

    before do
      review.create_reply(user:, comment: 'We are sorry')
    end

    it_behaves_like 'a protected carrier review controller action', :delete

    context 'when carrier user is authorized' do
      let(:url) { review_reply_url(review, as: user.to_param) }

      it 'redirects to review' do
        delete url
        expect(response).to redirect_to review_url(review)
        expect(review.reload.reply).to be_nil
      end

      context 'when delete fails' do
        before do
          allow_any_instance_of(Reply).to receive(:destroy).and_return(false)
        end

        it 'responds with unprocessable status' do
          delete url
          expect(response).to have_http_status :unprocessable_content
          expect(review.reload.reply).to have_attributes comment: 'We are sorry', user:
        end
      end
    end
  end
end
