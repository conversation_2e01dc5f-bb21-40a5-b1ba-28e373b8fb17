require 'rails_helper'

RSpec.describe 'Sidekiq Authorization' do
  describe 'GET /sidekiq' do
    context 'when user is not logged in' do
      it 'redirects to login' do
        get '/sidekiq'
        expect(response).to have_http_status :forbidden
      end
    end

    context 'when logged in user is not admin' do
      let(:user) { create :user, :broker, admin: false }

      it 'redirects to root' do
        get '/sidekiq', params: { as: user.to_param }
        expect(response).to have_http_status :forbidden
      end
    end

    context 'when logged in user is admin' do
      let(:user) { create :user, :broker, admin: true }

      it 'responds with ok status' do
        get '/sidekiq', params: { as: user.to_param }
        expect(response).to have_http_status :ok
      end
    end
  end
end
