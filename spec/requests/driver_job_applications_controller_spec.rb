require 'rails_helper'

RSpec.describe DriverJobApplicationsController do
  let(:carrier) { create :company }
  let(:driver_job) { create :driver_job, company: carrier }
  let(:city) { create :city }

  describe 'GET new' do
    it 'responds with ok' do
      get new_driver_job_application_url(driver_job)
      expect(response).to have_http_status :ok
    end
  end

  describe 'POST create' do
    let(:application_attributes) do
      attributes_for(:driver_job_application).merge(driver_job_id: driver_job.id, city_id: city.id)
    end

    it 'redirects to carrier profile page' do
      post driver_job_applications_url(driver_job),
           params: { driver_job_application: application_attributes }
      expect(response).to redirect_to carrier_url(carrier)
    end

    it 'responds with error for incomplete attributes' do
      post driver_job_applications_url(driver_job), params: { driver_job_application: { first_name: '<PERSON>' } }
      expect(response).to have_http_status :unprocessable_content
    end

    it 'persists utm params' do
      post driver_job_applications_url(driver_job),
           params: { driver_job_application: application_attributes, utm_source: 'facebook', utm_campaign: 'social' }
      expect(DriverJobApplication.last.utm_param).to have_attributes source: 'facebook', campaign: 'social'
    end
  end
end
