require 'rails_helper'

RSpec.describe BrokerageCompaniesController do
  let(:city) { create :city, name: 'Buffalo', state_code: 'NY' }
  let(:brokerage) { create :company, :broker, :with_city, :with_authority, city: }
  let!(:brokerage_profile) { create :brokerage_profile, company: brokerage }

  before do
    BrokerageCity.refresh
    Brokerage.es.import force: true, refresh: true, query: -> { es_import_query }
  end

  describe 'GET index' do
    it 'returns success status' do
      get brokerage_companies_url
      expect(response).to have_http_status :ok
    end
  end

  describe 'GET country', :elasticsearch do
    it 'returns success status' do
      get country_brokerage_companies_url('united-states')
      expect(response).to have_http_status :ok
    end

    it 'redirects to canonical slug' do
      get country_brokerage_companies_url('us')
      expect(response).to redirect_to country_brokerage_companies_url('united-states')
    end

    context 'when country is invalid' do
      it 'responds with not found' do
        expect { get country_brokerage_companies_url('country') }.to raise_error ActionController::RoutingError
      end
    end

    context 'when pagination is out of bounds' do
      it 'redirects to canonical page' do
        get country_brokerage_companies_url('united-states', page: 30)
        expect(response).to redirect_to country_brokerage_companies_url('united-states')
      end
    end
  end

  describe 'GET state', :elasticsearch do
    it 'returns success status' do
      get state_brokerage_companies_url('united-states', 'wisconsin')
      expect(response).to have_http_status :ok
    end

    it 'redirects to canonical slug' do
      get state_brokerage_companies_url('us', 'wisconsin')
      expect(response).to redirect_to state_brokerage_companies_url('united-states', 'wisconsin')
    end

    context 'when state is invalid' do
      it 'responds with not found' do
        expect do
          get state_brokerage_companies_url('united-states', 'british-columbia')
        end.to raise_error ActiveRecord::RecordNotFound
      end
    end

    context 'when pagination is out of bounds' do
      it 'redirects to canonical page' do
        get state_brokerage_companies_url('united-states', 'new-york', page: 30)
        expect(response).to redirect_to state_brokerage_companies_url('united-states', 'new-york')
      end
    end
  end

  describe 'GET city', :elasticsearch do
    it 'returns success status' do
      get city_brokerage_companies_url('united-states', 'new-york', 'buffalo')
      expect(response).to have_http_status :ok
    end

    it 'redirects to canonical slug' do
      get city_brokerage_companies_url('us', 'new-york', 'buffalo')
      expect(response).to redirect_to city_brokerage_companies_url('united-states', 'new-york', 'buffalo')
    end

    context 'when pagination is out of bounds' do
      it 'redirects to canonical page' do
        get city_brokerage_companies_url('united-states', 'new-york', 'buffalo', page: 30)
        expect(response).to redirect_to city_brokerage_companies_url('united-states', 'new-york', 'buffalo')
      end
    end

    context 'when user is logged in' do
      let(:user) { create :user, :carrier }

      it 'returns success status' do
        get city_brokerage_companies_url('united-states', 'new-york', 'buffalo', as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end
end
