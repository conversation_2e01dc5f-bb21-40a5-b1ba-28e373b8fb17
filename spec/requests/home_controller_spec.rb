require 'rails_helper'

RSpec.describe HomeController do
  describe 'GET index' do
    it 'responds with ok status' do
      get root_url
      expect(response).to have_http_status :ok
    end

    context 'when blocking unproxied requests' do
      around do |example|
        ClimateControl.modify BLOCK_UNPROXIED_REQUEST: 'true' do
          example.run
        end
      end

      context 'when request is proxied' do
        it 'responds with ok status' do
          get root_url, headers: { 'HTTP_X_FORWARDED_FOR' => '127.0.0.1, 172.71.210.38' }
          expect(response).to have_http_status :ok
        end
      end

      context 'when request is not proxied' do
        it 'responds with forbidden status' do
          get root_url
          expect(response).to have_http_status :forbidden
        end
      end
    end

    context 'with blocked user' do
      let(:user) { create :user, :broker, blocked: true }

      it 'responds with forbidden status' do
        get root_url(as: user.to_param)
        expect(response).to have_http_status :forbidden
      end
    end
  end
end
