require 'rails_helper'

RSpec.describe SearchController, :elasticsearch do
  let!(:carrier_profile) { create :carrier_profile, company: }
  let!(:company) { create :company, :carrier, :with_authority, :with_city, legal_name: 'Putnik Express' }
  let!(:brokerage) { create :company, :broker, :with_city, legal_name: 'Cooler Logistics' }
  let!(:brokerage_profile) { create :brokerage_profile, company: brokerage }

  before do
    Brokerage.es.import query: -> { es_import_query }, force: true, refresh: true
    Carrier.es.import query: -> { es_import_query }, force: true, refresh: true
    City.es.import query: -> { es_import_query }, force: true, refresh: true
  end

  describe 'GET #show' do
    context 'when query is blank' do
      it 'returns http no content' do
        get search_url
        expect(response).to have_http_status(:no_content)
      end
    end

    context 'when entity is carrier' do
      it 'returns http success' do
        get search_url(query: 'putnik')
        expect(response).to have_http_status(:ok)
      end
    end

    context 'when entity is broker' do
      it 'returns http success' do
        get search_url(query: 'cooler', entity: 'broker')
        expect(response).to have_http_status(:ok)
      end
    end
  end
end
