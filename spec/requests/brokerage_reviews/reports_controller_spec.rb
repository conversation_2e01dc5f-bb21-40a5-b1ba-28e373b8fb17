require 'rails_helper'
require_relative 'shared_examples'
require_relative '../brokerage_dashboard/shared_contexts'

RSpec.describe Reviews::ReportsController do
  include_context 'with verified brokerage profile user'

  let(:review) { create :brokerage_review, :carrier, :approved, company: }

  describe 'GET new' do
    let(:url) { new_brokerage_review_report_url(review) }

    it_behaves_like 'a protected brokerage review controller action', :get

    context 'when broker user is authorized' do
      let(:url) { new_brokerage_review_report_url(review, as: user.to_param) }

      it 'responds with ok status' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { brokerage_review_report_url(review) }

    it_behaves_like 'a protected brokerage review controller action', :post

    context 'when broker user is authorized' do
      let(:url) { brokerage_review_report_url(review, as: user.to_param) }

      it 'redirects to show' do
        post url, params: { brokerage_review_report: { reason: 'Inaccurate information',
                                                       description: 'None of this is true' } }
        expect(response).to redirect_to brokerage_review_report_url(review)
        expect(review.reload.report).to have_attributes reason: 'Inaccurate information',
                                                        description: 'None of this is true', user:
      end

      context 'when create fails' do
        before do
          allow_any_instance_of(BrokerageReviewReport).to receive(:save).and_return(false)
        end

        it 'responds with unprocessable status' do
          post url, params: { brokerage_review_report: { reason: 'Inaccurate information',
                                                         description: 'None of this is true' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'GET show' do
    let(:url) { brokerage_review_report_url(review) }

    it_behaves_like 'a protected brokerage review controller action', :get

    context 'when broker is authorized' do
      let(:url) { brokerage_review_report_url(review, as: user.to_param) }

      it 'responds with ok status' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET edit' do
    let(:url) { edit_brokerage_review_report_url(review) }

    it_behaves_like 'a protected brokerage review controller action', :get

    context 'when broker user is authorized' do
      let(:url) { edit_brokerage_review_report_url(review, as: user.to_param) }

      before do
        review.create_report(user:, reason: 'Inaccurate information', description: 'None of this is true')
      end

      it 'responds with ok status' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { brokerage_review_report_url(review) }

    before do
      review.create_report(user:, reason: 'Inaccurate information', description: 'None of this is true')
    end

    it_behaves_like 'a protected brokerage review controller action', :patch

    context 'when broker user is authorized' do
      let(:url) { brokerage_review_report_url(review, as: user.to_param) }

      it 'redirects to show' do
        patch url, params: { brokerage_review_report: { reason: 'Inaccurate information', description: 'Bogus!' } }
        expect(response).to redirect_to brokerage_review_report_url(review)
        expect(review.reload.report).to have_attributes reason: 'Inaccurate information', description: 'Bogus!', user:
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(BrokerageReviewReport).to receive(:save).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url, params: { brokerage_review_report: { reason: 'Inaccurate information', description: 'Bogus!' } }
          expect(response).to have_http_status :unprocessable_content
          expect(review.reload.report).to have_attributes reason: 'Inaccurate information',
                                                          description: 'None of this is true', user:
        end
      end
    end
  end

  describe 'DELETE destroy' do
    let(:url) { brokerage_review_report_url(review) }

    before do
      review.create_report(user:, reason: 'Inaccurate information', description: 'None of this is true')
    end

    it_behaves_like 'a protected brokerage review controller action', :delete

    context 'when broker user is authorized' do
      let(:url) { brokerage_review_report_url(review, as: user.to_param) }

      it 'redirects to review' do
        delete url
        expect(response).to redirect_to brokerage_review_url(review)
        expect(review.reload.report).to be_nil
      end

      context 'when delete fails' do
        before do
          allow_any_instance_of(BrokerageReviewReport).to receive(:destroy).and_return(false)
        end

        it 'responds with unprocessable status' do
          delete url
          expect(response).to have_http_status :unprocessable_content
          expect(review.reload.report).to have_attributes reason: 'Inaccurate information',
                                                          description: 'None of this is true', user:
        end
      end
    end
  end
end
