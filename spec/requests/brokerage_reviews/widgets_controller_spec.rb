require 'rails_helper'
require_relative 'shared_examples'
require_relative '../brokerage_dashboard/shared_contexts'

RSpec.describe BrokerageReviews::WidgetsController do
  include_context 'with verified brokerage profile user'

  let(:review) { create :brokerage_review, :carrier, :approved, company: }
  let!(:widget) { create :widget, :broker, company:, widget_type: 'review' }

  describe 'PATCH update' do
    let(:url) { brokerage_review_widget_url(review) }

    it_behaves_like 'a protected brokerage review controller action', :patch

    context 'when brokerage user is authorized' do
      let(:url) { brokerage_review_widget_url(review, as: user.to_param) }

      it 'redirects to review' do
        patch url
        expect(response).to redirect_to brokerage_review_url(review)
        expect(widget.brokerage_reviews).to include review
      end

      context 'when update fails' do
        before do
          allow(BrokerageReviews::AddToWidget).to receive(:call).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url
          expect(response).to have_http_status :unprocessable_content
          expect(widget.brokerage_reviews).not_to include review
        end
      end
    end
  end

  describe 'DELETE destroy' do
    let(:url) { brokerage_review_widget_url(review) }

    it_behaves_like 'a protected brokerage review controller action', :delete

    context 'when brokerage user is authorized' do
      let(:url) { brokerage_review_widget_url(review, as: user.to_param) }

      before do
        WidgetBrokerageReview.create!(brokerage_review: review, widget:)
      end

      it 'redirects to review' do
        delete url
        expect(response).to redirect_to brokerage_review_url(review)
        expect(widget.brokerage_reviews).not_to include review
      end

      context 'when delete fails' do
        before do
          allow(BrokerageReviews::RemoveFromWidget).to receive(:call).and_return(false)
        end

        it 'responds with unprocessable status' do
          delete url
          expect(response).to have_http_status :unprocessable_content
          expect(widget.brokerage_reviews).to include review
        end
      end
    end
  end
end
