require 'rails_helper'
require_relative '../shared_examples'

RSpec.describe BrokerageDashboard::Profile::ContactsController do
  let(:brokerage_profile) { create :brokerage_profile }
  let(:brokerage) { brokerage_profile.brokerage }
  let(:brokerage_profile_user) { create :brokerage_profile_user, :verified, brokerage_profile: }
  let(:user) { brokerage_profile_user.user }

  describe 'GET index' do
    let(:url) { brokerage_dashboard_profile_contacts_url(brokerage) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is a verified profile user' do
      let(:url) { brokerage_dashboard_profile_contacts_url(brokerage, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET new' do
    let(:url) { new_brokerage_dashboard_profile_contact_url(brokerage) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is a verified profile user' do
      let(:url) { new_brokerage_dashboard_profile_contact_url(brokerage, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET edit' do
    let(:contact) { create :brokerage_profile_contact, brokerage_profile: }
    let(:url) { edit_brokerage_dashboard_profile_contact_url(brokerage, contact) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is a verified profile user' do
      let(:url) { edit_brokerage_dashboard_profile_contact_url(brokerage, contact, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { brokerage_dashboard_profile_contacts_url(brokerage) }

    it_behaves_like 'a brokerage dashboard controller action', :post

    context 'when brokerage user is a verified profile user' do
      let(:url) { brokerage_dashboard_profile_contacts_url(brokerage, as: user.to_param) }

      it 'redirects to index page' do
        post url, params: { brokerage_profile_contact: attributes_for(:brokerage_profile_contact) }
        expect(response).to redirect_to brokerage_dashboard_profile_contacts_url(brokerage)
      end

      it 'creates a new contact' do
        expect do
          post url, params: { brokerage_profile_contact: attributes_for(:brokerage_profile_contact) }
        end.to change(BrokerageProfileContact, :count).by(1)
      end

      context 'when contact is invalid' do
        it 'does not create a new contact' do
          expect do
            post url, params: { brokerage_profile_contact: attributes_for(:brokerage_profile_contact, type: nil) }
          end.not_to change(BrokerageProfileContact, :count)
        end

        it 'returns unprocessable status' do
          post url, params: { brokerage_profile_contact: attributes_for(:brokerage_profile_contact, type: nil) }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'PATCH update' do
    let(:contact) { create :brokerage_profile_contact, brokerage_profile: }
    let(:url) { brokerage_dashboard_profile_contact_url(brokerage, contact) }

    it_behaves_like 'a brokerage dashboard controller action', :patch

    context 'when brokerage user is a verified profile user' do
      let(:url) { brokerage_dashboard_profile_contact_url(brokerage, contact, as: user.to_param) }

      it 'redirects to index page' do
        patch url, params: { brokerage_profile_contact: { name: 'Fred Rogers' } }
        expect(response).to redirect_to brokerage_dashboard_profile_contacts_url(brokerage)
      end

      it 'updates the contact' do
        patch url, params: { brokerage_profile_contact: { name: 'Fred Rogers' } }
        expect(contact.reload.name).to eq 'Fred Rogers'
      end

      context 'when contact is invalid' do
        it 'does not update the contact' do
          patch url, params: { brokerage_profile_contact: { type: nil } }
          expect(contact.reload.name).not_to be_nil
        end

        it 'returns unprocessable status' do
          patch url, params: { brokerage_profile_contact: { type: nil } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end

    describe 'DELETE destroy' do
      let!(:contact) { create :brokerage_profile_contact, brokerage_profile: }
      let(:url) { brokerage_dashboard_profile_contact_url(brokerage, contact) }

      it_behaves_like 'a brokerage dashboard controller action', :delete

      context 'when brokerage user is a verified profile user' do
        let(:url) { brokerage_dashboard_profile_contact_url(brokerage, contact, as: user.to_param) }

        it 'redirects to index page' do
          delete url
          expect(response).to redirect_to brokerage_dashboard_profile_contacts_url(brokerage)
        end

        it 'deletes the contact' do
          expect do
            delete url
          end.to change(BrokerageProfileContact, :count).by(-1)
        end

        context 'when destroy fails' do
          before do
            allow_any_instance_of(BrokerageProfileContact).to receive(:destroy).and_return(false)
          end

          it 'does not delete the contact' do
            expect do
              delete url
            end.not_to change(BrokerageProfileContact, :count)
          end

          it 'returns unprocessable status' do
            delete url
            expect(response).to have_http_status :unprocessable_content
          end
        end
      end
    end

    describe 'PATCH make_default' do
      let!(:contact) { create :brokerage_profile_contact, brokerage_profile: }
      let(:url) { make_default_brokerage_dashboard_profile_contact_url(brokerage, contact) }

      it_behaves_like 'a brokerage dashboard controller action', :patch

      context 'when brokerage user is a verified profile user' do
        let(:url) { make_default_brokerage_dashboard_profile_contact_url(brokerage, contact, as: user.to_param) }

        it 'redirects to index page' do
          patch url
          expect(response).to redirect_to brokerage_dashboard_profile_contacts_url(brokerage)
        end

        it 'makes the contact the default' do
          patch url
          expect(contact.reload.default).to be true
        end
      end
    end
  end
end
