require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe BrokerageDashboard::WidgetsController do
  include_context 'with verified brokerage profile user'

  describe 'GET #index' do
    let(:url) { brokerage_dashboard_widgets_url(brokerage) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_widgets_url(brokerage, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET #edit' do
    let(:widget) { create :widget, :broker, company: }
    let(:url) { edit_brokerage_dashboard_widget_url(brokerage, widget) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is verified' do
      let(:url) { edit_brokerage_dashboard_widget_url(brokerage, widget, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST #create' do
    let(:widget_type) { 'collect_review' }
    let(:url) { brokerage_dashboard_widgets_url(brokerage) }

    it_behaves_like 'a brokerage dashboard controller action', :post

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_widgets_url(brokerage, as: user.to_param) }

      it 'creates a widget and redirects to edit page' do
        post url, params: { widget_type: }
        expect(response).to redirect_to(edit_brokerage_dashboard_widget_url(brokerage, Widget.last))
      end
    end
  end

  describe 'PATCH #update' do
    let(:widget) { create :widget, :broker, company: }
    let(:url) { brokerage_dashboard_widget_url(brokerage, widget) }

    it_behaves_like 'a brokerage dashboard controller action', :patch

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_widget_url(brokerage, widget, as: user.to_param) }

      it 'updates the widget and redirects to edit page' do
        patch url, params: { widget: { style: 'new_style' } }
        expect(response).to redirect_to(edit_brokerage_dashboard_widget_url(brokerage, widget))
      end

      context 'when the widget update fails' do
        before do
          allow_any_instance_of(Widget).to receive(:update).and_return(false)
        end

        it 'renders the edit template' do
          patch url, params: { widget: { style: 'invalid_style' } }
          expect(response).to have_http_status :ok
        end
      end
    end
  end
end
