require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe BrokerageDashboard::SubscriptionsController do
  include_context 'with verified brokerage profile user'

  describe 'GET show' do
    let(:url) { brokerage_dashboard_subscription_url(company) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_subscription_url(company, as: user.to_param) }

      context 'when active subscription exists' do
        let(:subscription) { create :subscription, :brokerage, resource: brokerage_profile }

        before do
          create :access_package, :brokerage, resource: brokerage_profile, subscription:
        end

        it 'responds with ok' do
          get url
          expect(response).to have_http_status :ok
        end
      end

      context 'when active subscription does not exist' do
        it 'responds with ok' do
          get url
          expect(response).to have_http_status :ok
        end
      end
    end
  end

  describe 'GET edit' do
    let(:url) { edit_brokerage_dashboard_subscription_url(company) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is verified' do
      let(:url) { edit_brokerage_dashboard_subscription_url(company, as: user.to_param) }

      context 'when subscription does not exist' do
        it 'redirects to show' do
          get url
          expect(response).to redirect_to brokerage_dashboard_subscription_url(company)
        end
      end

      context 'when subscription exists' do
        let(:session) { double }
        let(:brokerage_subscription) { create :subscription, :brokerage, resource: brokerage_profile }
        let(:stripe_subscription) { double }

        before do
          allow(Stripe::BillingPortal::Session).to receive(:create).and_return(session)
          allow(session).to receive(:url).and_return 'https://api.stripe.com'
          allow(Stripe::Subscription).to receive(:retrieve).with(brokerage_subscription.external_id)
                                           .and_return(stripe_subscription)
          allow(stripe_subscription).to receive(:customer).and_return('cus_123')

          create :access_package, :brokerage, resource: brokerage_profile, subscription: brokerage_subscription
        end

        it 'redirects to stripe session' do
          get url
          expect(response).to redirect_to 'https://api.stripe.com'
        end
      end
    end
  end
end
