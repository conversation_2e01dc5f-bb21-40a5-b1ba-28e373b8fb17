require 'rails_helper'
require_relative '../shared_examples'
require_relative '../shared_contexts'

RSpec.describe BrokerageDashboard::Overview::HomeController do
  include_context 'with verified brokerage profile user'

  describe 'GET show' do
    let(:url) { brokerage_dashboard_overview_root_url(brokerage) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when broker user is verified' do
      let(:url) { brokerage_dashboard_overview_root_url(brokerage, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end
end
