require 'rails_helper'
require_relative '../shared_examples'
require_relative '../shared_contexts'

RSpec.describe BrokerageDashboard::Overview::ShippersController, :elasticsearch do
  include_context 'with verified brokerage profile user'

  let(:analytics_company) { create :analytics_company }

  before do
    visit = create :analytics_visit, :completed, company: analytics_company

    create :analytics_event,
           name: 'Page Viewed', visit:,
           properties: { id: company.id, entity_type: 'broker', route: 'brokerages#show', url: brokerage_url(company) }

    Analytics::Events::CreateShipperEventJob.drain
    Elastic::Analytics::ShipperEvents::BulkImport.call
  end

  describe 'GET index' do
    let(:url) { brokerage_dashboard_overview_shippers_url(company) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user does not have access' do
      let(:url) { brokerage_dashboard_overview_shippers_url(company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_overview_shippers_url(company, as: user.to_param) }

      before do
        create :access_package, :brokerage, resource: brokerage_profile
      end

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { brokerage_dashboard_overview_shipper_url(company, analytics_company) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_overview_shipper_url(company, analytics_company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end
end
