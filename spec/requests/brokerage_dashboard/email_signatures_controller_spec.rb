require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe BrokerageDashboard::EmailSignaturesController do
  include_context 'with verified brokerage profile user'

  describe 'GET show' do
    let(:url) { brokerage_dashboard_email_signature_url(company) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_email_signature_url(company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET preview' do
    let(:url) { preview_brokerage_dashboard_email_signature_url(company) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is verified' do
      let(:url) do
        preview_brokerage_dashboard_email_signature_url(company, as: user.to_param, name: Faker::Name.name,
                                                                 email: Faker::Internet.email)
      end

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end
end
