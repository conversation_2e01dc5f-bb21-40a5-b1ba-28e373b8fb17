require 'rails_helper'
require_relative '../shared_examples'
require_relative '../shared_contexts'

RSpec.describe BrokerageDashboard::Analytics::IntegrationsController do
  include_context 'with verified brokerage profile user'

  describe 'GET index' do
    let(:url) { brokerage_dashboard_analytics_integrations_url(company) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_analytics_integrations_url(company, as: user.to_param) }

      before do
        create :analytics_integration, :broker, :slack, company:
      end

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { brokerage_dashboard_analytics_integrations_url(company) }

    it_behaves_like 'a brokerage dashboard controller action', :post

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_analytics_integrations_url(company, as: user.to_param) }

      it 'creates an integration' do
        expect do
          post url, params: { analytics_integration: { provider: 'slack' } }
        end.to change(Analytics::Integration, :count).by(1)
      end

      context 'with slack provider' do
        it 'redirects to the authentication URL' do
          post url, params: { analytics_integration: { provider: 'slack' } }
          expect(response).to redirect_to %r{https://slack.com/oauth/v2/authorize}
        end
      end

      context 'with teams provider' do
        it 'redirects to the authentication URL' do
          post url, params: { analytics_integration: { provider: 'teams' } }
          expect(response).to redirect_to %r{https://login.microsoftonline.com/common/oauth2/v2.0/authorize}
        end
      end
    end
  end
end
