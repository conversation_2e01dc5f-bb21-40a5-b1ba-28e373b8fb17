require 'rails_helper'
require_relative '../shared_examples'
require_relative '../shared_contexts'

RSpec.describe BrokerageDashboard::Analytics::FeedsController do
  include_context 'with verified brokerage profile user'

  let!(:feed) { create :analytics_company_event_feed, :broker, company: }

  describe 'GET index' do
    let(:url) { brokerage_dashboard_analytics_feeds_url(company) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_analytics_feeds_url(company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET new' do
    let(:url) { new_brokerage_dashboard_analytics_feed_url(company) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is verified' do
      let(:url) { new_brokerage_dashboard_analytics_feed_url(company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET edit' do
    let(:url) { edit_brokerage_dashboard_analytics_feed_url(company, feed) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is verified' do
      let(:url) { edit_brokerage_dashboard_analytics_feed_url(company, feed, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { brokerage_dashboard_analytics_feeds_url(company) }

    it_behaves_like 'a brokerage dashboard controller action', :post

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_analytics_feeds_url(company, as: user.to_param) }

      context 'with valid params' do
        it 'creates a feed' do
          expect do
            post url, params: { analytics_company_event_feed: { name: 'Feed' } }
          end.to change(Analytics::CompanyEventFeed, :count).by(1)
        end

        it 'redirects to feed' do
          post url, params: { analytics_company_event_feed: { name: 'Feed' } }
          expect(response).to redirect_to brokerage_dashboard_analytics_feeds_url(company)
        end
      end

      context 'with invalid params' do
        it 'does not create a feed' do
          expect do
            post url, params: { analytics_company_event_feed: { name: nil } }
          end.not_to change(Analytics::CompanyEventFeed, :count)
        end

        it 'responds with ok' do
          post url, params: { analytics_company_event_feed: { name: nil } }
          expect(response).to have_http_status :ok
        end
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { brokerage_dashboard_analytics_feed_url(company, feed) }

    it_behaves_like 'a brokerage dashboard controller action', :patch

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_analytics_feed_url(company, feed, as: user.to_param) }

      context 'with valid params' do
        it 'updates the feed' do
          patch url, params: { analytics_company_event_feed: { name: 'New Feed' } }
          feed.reload
          expect(feed.name).to eq 'New Feed'
        end

        it 'redirects to feed' do
          patch url, params: { analytics_company_event_feed: { name: 'New Feed' } }
          expect(response).to redirect_to brokerage_dashboard_analytics_feeds_url(company)
        end
      end

      context 'with invalid params' do
        it 'does not update the feed' do
          patch url, params: { analytics_company_event_feed: { name: nil } }
          feed.reload
          expect(feed.name).not_to be_nil
        end

        it 'responds with ok' do
          patch url, params: { analytics_company_event_feed: { name: nil } }
          expect(response).to have_http_status :ok
        end
      end
    end
  end

  describe 'DELETE destroy' do
    let(:url) { brokerage_dashboard_analytics_feed_url(company, feed) }

    it_behaves_like 'a brokerage dashboard controller action', :delete

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_analytics_feed_url(company, feed, as: user.to_param) }

      it 'destroys the feed' do
        expect do
          delete url
        end.to change(Analytics::CompanyEventFeed, :count).by(-1)
      end

      it 'redirects to index' do
        delete url
        expect(response).to redirect_to brokerage_dashboard_analytics_feeds_url(company)
      end
    end
  end

  describe 'POST duplicate' do
    let(:url) { duplicate_brokerage_dashboard_analytics_feed_url(company, feed) }

    it_behaves_like 'a brokerage dashboard controller action', :post

    context 'when brokerage user is verified' do
      let(:url) { duplicate_brokerage_dashboard_analytics_feed_url(company, feed, as: user.to_param) }

      it 'duplicates the feed' do
        expect do
          post url
        end.to change(Analytics::CompanyEventFeed, :count).by(1)
      end

      it 'redirects to edit' do
        post url
        expect(response).to(
          redirect_to(edit_brokerage_dashboard_analytics_feed_url(company, Analytics::CompanyEventFeed.last))
        )
      end
    end
  end

  describe 'POST export' do
    let(:url) { export_brokerage_dashboard_analytics_feed_url(company, feed) }

    it_behaves_like 'a brokerage dashboard controller action', :post

    context 'when brokerage user is verified' do
      let(:url) { export_brokerage_dashboard_analytics_feed_url(company, feed, as: user.to_param) }

      it 'creates an export' do
        expect do
          post url, params: { email: Faker::Internet.email }
        end.to change(Analytics::CompanyEventFeedExport, :count).by(1)
      end
    end
  end
end
