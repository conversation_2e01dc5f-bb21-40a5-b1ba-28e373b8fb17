require 'rails_helper'
require_relative '../../shared_examples'
require_relative '../../shared_contexts'

RSpec.describe BrokerageDashboard::Analytics::Feeds::IntegrationsController do
  include_context 'with verified brokerage profile user'

  let!(:feed) { create :analytics_company_event_feed, :broker, company: }

  describe 'GET index' do
    let(:url) { brokerage_dashboard_analytics_feed_integrations_url(company, feed) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_analytics_feed_integrations_url(company, feed, as: user.to_param) }

      before do
        create :analytics_integration, :broker, :active, :slack, company:
      end

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET edit' do
    let(:url) { edit_brokerage_dashboard_analytics_feed_integration_url(company, feed, notification) }
    let!(:notification) { create :analytics_company_event_feed_notification, :slack, feed: }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is verified' do
      let(:url) do
        edit_brokerage_dashboard_analytics_feed_integration_url(company, feed, notification, as: user.to_param)
      end

      context 'with slack provider' do
        let(:body) { Rails.root.join('spec/fixtures/slack/conversations/list.json').read }

        before do
          stub_request(:post, 'https://slack.com/api/conversations.list')
            .to_return(status: 200, body:, headers: { 'Content-Type' => 'application/json' })
        end

        it 'responds with ok' do
          get url
          expect(response).to have_http_status :ok
        end
      end

      context 'with teams provider' do
        let!(:notification) { create :analytics_company_event_feed_notification, :teams, feed: }

        let(:team_body) { Rails.root.join('spec/fixtures/microsoft/graph/me/joined_teams.json').read }
        let(:channel_body) { Rails.root.join('spec/fixtures/microsoft/graph/teams/channels/list.json').read }
        let(:team_id) { notification.properties.teams.team_id }

        before do
          stub_request(:get, 'https://graph.microsoft.com/v1.0/me/joinedTeams')
            .to_return(status: 200, body: team_body, headers: { 'Content-Type' => 'application/json' })

          stub_request(:get, "https://graph.microsoft.com/v1.0/teams/#{team_id}/channels")
            .to_return(status: 200, body: channel_body, headers: { 'Content-Type' => 'application/json' })
        end

        it 'responds with ok' do
          get url
          expect(response).to have_http_status :ok
        end
      end
    end
  end

  describe 'POST create' do
    let(:url) { brokerage_dashboard_analytics_feed_integrations_url(company, feed) }

    it_behaves_like 'a brokerage dashboard controller action', :post

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_analytics_feed_integrations_url(company, feed, as: user.to_param) }
      let(:integration) { create :analytics_integration, :hubspot, :broker, company: }

      it 'creates a new notification' do
        expect do
          post url, params: { analytics_company_event_feed_notification: { notification_type: 'hubspot',
                                                                           integration_id: integration.id } }
        end.to change(feed.notifications, :count).by(1)
      end

      it 'redirects to edit page' do
        post url, params: { analytics_company_event_feed_notification: { notification_type: 'hubspot',
                                                                         integration_id: integration.id } }
        expect(response).to(
          redirect_to(
            edit_brokerage_dashboard_analytics_feed_integration_url(company, feed, feed.reload.notifications.first)
          )
        )
      end

      context 'when the notification is invalid' do
        before do
          allow_any_instance_of(Analytics::CompanyEventFeedNotification).to receive(:save).and_return(false)
        end

        it 'responds with unprocessable entity' do
          post url, params: { analytics_company_event_feed_notification: { notification_type: 'hubspot' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { brokerage_dashboard_analytics_feed_integration_url(company, feed, notification) }
    let!(:notification) { create :analytics_company_event_feed_notification, :hubspot, feed: }

    it_behaves_like 'a brokerage dashboard controller action', :patch

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_analytics_feed_integration_url(company, feed, notification, as: user.to_param) }

      it 'updates the notification' do
        patch url, params: { analytics_company_event_feed_notification: { interval: 'daily' } }
        expect(notification.reload.interval).to eq('daily')
      end

      it 'redirects to the feeds index' do
        patch url, params: { analytics_company_event_feed_notification: { interval: 'daily' } }
        expect(response).to redirect_to brokerage_dashboard_analytics_feeds_url(company)
      end

      context 'when the notification is invalid' do
        before do
          allow_any_instance_of(Analytics::CompanyEventFeedNotification).to receive(:update).and_return(false)
        end

        it 'responds with unprocessable entity' do
          patch url, params: { analytics_company_event_feed_notification: { interval: 'daily' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'DELETE destroy' do
    let(:url) { brokerage_dashboard_analytics_feed_integration_url(company, feed, notification) }
    let!(:notification) { create :analytics_company_event_feed_notification, :hubspot, feed: }

    it_behaves_like 'a brokerage dashboard controller action', :delete

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_analytics_feed_integration_url(company, feed, notification, as: user.to_param) }

      it 'destroys the notification' do
        expect { delete url }.to change(feed.notifications, :count).by(-1)
      end

      it 'redirects to the feeds index' do
        delete url
        expect(response).to redirect_to brokerage_dashboard_analytics_feeds_url(company)
      end
    end
  end
end
