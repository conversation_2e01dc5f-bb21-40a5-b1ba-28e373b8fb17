require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe BrokerageDashboard::ReviewsController do
  include_context 'with verified brokerage profile user'

  describe 'GET show' do
    let(:url) { brokerage_dashboard_reviews_url(brokerage) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when broker user is a verified profile user' do
      let(:url) { brokerage_dashboard_reviews_url(brokerage, as: user.to_param) }

      before do
        create :brokerage_review, :carrier, :approved, company:
      end

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end
end
