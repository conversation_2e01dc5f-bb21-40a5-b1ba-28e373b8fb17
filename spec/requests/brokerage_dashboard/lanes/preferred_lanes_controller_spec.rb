require 'rails_helper'
require_relative '../shared_examples'
require_relative '../shared_contexts'

RSpec.describe BrokerageDashboard::Lanes::PreferredLanesController do
  include_context 'with verified brokerage profile user'

  describe 'GET show' do
    let(:url) { brokerage_dashboard_lanes_preferred_lanes_url(company) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_lanes_preferred_lanes_url(company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { brokerage_dashboard_lanes_preferred_lanes_url(company) }

    it_behaves_like 'a brokerage dashboard controller action', :patch

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_lanes_preferred_lanes_url(company, as: user.to_param) }
      let(:pickup) { create :city }
      let(:dropoff) { create :city }

      it 'updates attributes' do
        patch url, params: {
          brokerage_profile: {
            preferred_lanes_attributes: { '0' => { pickup_city_id: pickup.id, dropoff_city_id: dropoff.id } }
          }
        }

        expect(brokerage_profile.preferred_lanes.pluck(:pickup_city_id, :dropoff_city_id)).to(
          eq [[pickup.id, dropoff.id]]
        )
      end

      it 'queues up jobs' do
        patch url, params: {
          brokerage_profile: {
            preferred_lanes_attributes: { '0' => { pickup_city_id: pickup.id, dropoff_city_id: dropoff.id } }
          }
        }

        expect(Elastic::SyncRecordJob.jobs.pick('args')).to eq [brokerage.to_gid.to_s]
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(BrokerageProfile).to receive(:update).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url, params: {
            brokerage_profile: {
              preferred_lanes_attributes: { '0' => { pickup_city_id: pickup.id, dropoff_city_id: dropoff.id } }
            }
          }

          expect(response).to have_http_status :unprocessable_content
        end

        it 'does not queue up elastic job' do
          patch url, params: {
            brokerage_profile: {
              preferred_lanes_attributes: { '0' => { pickup_city_id: pickup.id, dropoff_city_id: dropoff.id } }
            }
          }

          expect(Elastic::SyncRecordJob.jobs.size).to eq 0
        end
      end
    end
  end
end
