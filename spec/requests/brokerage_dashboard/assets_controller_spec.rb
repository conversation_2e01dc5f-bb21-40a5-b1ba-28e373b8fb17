require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe BrokerageDashboard::AssetsController do
  include_context 'with verified brokerage profile user'

  describe 'GET index' do
    let(:url) { brokerage_dashboard_assets_url(brokerage) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is a verified profile user' do
      let(:url) { brokerage_dashboard_assets_url(brokerage, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET edit' do
    let(:asset) { create :brokerage_profile_asset, brokerage_profile: }
    let(:url) { edit_brokerage_dashboard_asset_url(brokerage, asset) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is a verified profile user' do
      let(:url) { edit_brokerage_dashboard_asset_url(brokerage, asset, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { brokerage_dashboard_assets_url(brokerage) }

    it_behaves_like 'a brokerage dashboard controller action', :post

    context 'when brokerage user is a verified profile user' do
      let(:url) { brokerage_dashboard_assets_url(brokerage, as: user.to_param) }

      context 'when asset is created' do
        it 'redirects to index' do
          post url, params: { brokerage_profile_asset: { file: fixture_file_upload('logo.png', 'image/png') } }
          expect(response).to redirect_to brokerage_dashboard_assets_url(brokerage)
        end
      end

      context 'when asset creation fails' do
        it 'responds with unprocessable entity' do
          post url, params: { brokerage_profile_asset: { file: nil } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'PATCH update' do
    let(:asset) { create :brokerage_profile_asset, brokerage_profile: }
    let(:url) { brokerage_dashboard_asset_url(brokerage, asset) }

    it_behaves_like 'a brokerage dashboard controller action', :patch

    context 'when brokerage user is a verified profile user' do
      let(:url) { brokerage_dashboard_asset_url(brokerage, asset, as: user.to_param) }

      it 'updates attributes' do
        patch url, params: { brokerage_profile_asset: { title: 'Trip to Chicago' } }
        expect(asset.reload.title).to eq 'Trip to Chicago'
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(BrokerageProfileAsset).to receive(:update).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url, params: { brokerage_profile_asset: { title: 'Trip to Chicago' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'DELETE destroy' do
    let(:asset) { create :brokerage_profile_asset, brokerage_profile: }
    let(:url) { brokerage_dashboard_asset_url(brokerage, asset) }

    it_behaves_like 'a brokerage dashboard controller action', :delete

    context 'when brokerage user is a verified profile user' do
      let(:url) { brokerage_dashboard_asset_url(brokerage, asset, as: user.to_param) }

      context 'when destroy succeeds' do
        it 'redirects to index' do
          delete url
          expect(response).to redirect_to brokerage_dashboard_assets_url(brokerage)
        end
      end

      context 'when destroy fails' do
        before do
          allow_any_instance_of(BrokerageProfileAsset).to receive(:destroy).and_return(false)
        end

        it 'responds with unprocessable status' do
          delete url
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end
end
