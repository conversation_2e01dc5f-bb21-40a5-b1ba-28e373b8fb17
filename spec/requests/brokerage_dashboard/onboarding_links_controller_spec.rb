require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe BrokerageDashboard::OnboardingLinksController do
  include_context 'with verified brokerage profile user'

  describe 'GET show' do
    let(:url) { brokerage_dashboard_onboarding_link_url(brokerage) }

    it_behaves_like 'a brokerage dashboard controller action', :get

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_onboarding_link_url(brokerage, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end

      context 'with existing onboarding information' do
        before do
          create :brokerage_onboarding, brokerage_profile:
        end

        it 'displays the current onboarding information' do
          get url
          expect(response.body).to include 'Current Onboarding Information'
        end
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { brokerage_dashboard_onboarding_link_url(brokerage) }

    it_behaves_like 'a brokerage dashboard controller action', :patch

    context 'when brokerage user is verified' do
      let(:url) { brokerage_dashboard_onboarding_link_url(brokerage, as: user.to_param) }

      let(:valid_params) do
        {
          brokerage_onboarding: {
            onboarding_system: 'Highway',
            onboarding_link: 'https://highway.example.com/onboarding'
          }
        }
      end

      context 'with valid parameters' do
        it 'creates new onboarding information' do
          expect do
            patch url, params: valid_params
          end.to change(BrokerageOnboarding, :count).by(1)
        end

        it 'redirects to onboarding link page with success message' do
          patch url, params: valid_params
          expect(response).to redirect_to(brokerage_dashboard_onboarding_link_path(brokerage))
          expect(flash[:notice]).to eq('Onboarding link updated successfully.')
        end
      end

      context 'with existing onboarding information' do
        let!(:onboarding) { create(:brokerage_onboarding, brokerage_profile: brokerage.profile) }

        it 'updates existing onboarding information' do
          expect do
            patch url, params: valid_params
          end.not_to change(BrokerageOnboarding, :count)

          onboarding.reload
          expect(onboarding.onboarding_system).to eq('Highway')
          expect(onboarding.onboarding_link).to eq('https://highway.example.com/onboarding')
        end
      end

      context 'with invalid parameters' do
        let(:invalid_params) { { brokerage_onboarding: { onboarding_system: '', onboarding_link: 'not-a-url' } } }

        it 'does not create onboarding information' do
          expect do
            patch url, params: invalid_params
          end.not_to change(BrokerageOnboarding, :count)
        end

        it 'renders show template with errors' do
          patch url, params: invalid_params
          expect(response).to have_http_status :unprocessable_content
          expect(response.body).to include('Onboarding Link')
        end
      end
    end
  end
end
