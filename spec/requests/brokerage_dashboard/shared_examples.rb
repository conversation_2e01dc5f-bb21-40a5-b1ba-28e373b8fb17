RSpec.shared_examples 'a brokerage dashboard controller action' do |method|
  context 'when user not logged in' do
    it 'redirects to login' do
      public_send method, url
      expect(response).to redirect_to sign_in_url
    end
  end

  context 'when user is logged in' do
    context 'when user is not a broker' do
      let(:user) { create :user, :carrier }

      it 'redirects back to root' do
        public_send method, Functions::AppendToUrl.call(url, as: user.to_param)
        expect(response).to redirect_to root_url
      end
    end

    context 'when broker is not a verified profile user' do
      let(:brokerage_profile_user) { create :brokerage_profile_user, :pending, brokerage_profile: }

      it 'redirects to brokerage claim' do
        public_send method, Functions::AppendToUrl.call(url, as: user.to_param)
        expect(response).to redirect_to brokerage_claim_url(brokerage, brokerage_profile_user)
      end
    end
  end
end
