require 'rails_helper'
require_relative 'shared_examples'

RSpec.describe Dashboard::ProfilesController do
  describe 'GET show' do
    let(:url) { dashboard_profile_url }

    it_behaves_like 'a dashboard controller action', :get

    context 'when user is logged in as broker' do
      let(:user) { create :user, :broker }
      let(:url) { dashboard_profile_url(as: user.to_param) }

      it 'responds with ok status' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { dashboard_profile_url }

    it_behaves_like 'a dashboard controller action', :patch

    context 'when user is logged in as broker' do
      let(:user) { create :user, :broker }
      let(:url) { dashboard_profile_url(as: user.to_param) }

      context 'when update succeeds' do
        it 'redirects to show' do
          patch url, params: { user: { first_name: '<PERSON>', last_name: '<PERSON>' } }
          expect(response).to redirect_to dashboard_profile_url
        end

        it 'updates user' do
          patch url, params: { user: { first_name: '<PERSON>', last_name: '<PERSON>' } }
          expect(user.reload).to have_attributes first_name: '<PERSON>', last_name: '<PERSON>'
        end
      end

      context 'when update fails' do
        it 'responds with unprocessable status' do
          patch url, params: { user: { first_name: '<PERSON>', last_name: nil } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end
end
