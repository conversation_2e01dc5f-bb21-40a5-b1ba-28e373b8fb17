require 'rails_helper'

RSpec.describe DriverJobsController do
  let!(:carrier_profile) { create :carrier_profile, company: }
  let!(:company) { create :company, :carrier, :with_city }

  describe 'GET index', :elasticsearch do
    before do
      Carrier.es.import force: true, refresh: true, query: -> { es_import_query }
    end

    context 'when driver jobs exist' do
      before do
        create :driver_job, :published, company:
      end

      it 'responds with success' do
        get carrier_driver_jobs_url(company)
        expect(response).to have_http_status :ok
      end
    end

    context 'when driver jobs do not exist' do
      it 'redirects to carrier' do
        get carrier_driver_jobs_url(company)
        expect(response).to redirect_to carrier_url(company)
      end
    end
  end

  describe 'GET show' do
    let(:driver_job) { create :driver_job, company: }

    it 'responds with success' do
      get driver_job_url(driver_job)
      expect(response).to have_http_status :ok
    end
  end
end
