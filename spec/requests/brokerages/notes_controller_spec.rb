require 'rails_helper'

RSpec.describe Brokerages::NotesController do
  let(:brokerage) { create :company, :broker }

  shared_examples 'an authenticated action' do |method|
    context 'when user is not logged in' do
      it 'redirects to login' do
        public_send method, url
        expect(response).to redirect_to sign_in_url
      end
    end
  end

  describe 'GET new' do
    let(:url) { new_brokerage_note_url(brokerage) }

    it_behaves_like 'an authenticated action', :get

    context 'when user is signed in' do
      let(:user) { create :user, :broker }
      let(:url) { new_brokerage_note_url(brokerage, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { brokerage_note_url(brokerage) }

    it_behaves_like 'an authenticated action', :post

    context 'when user is signed in' do
      let(:user) { create :user, :broker }
      let(:url) { brokerage_note_url(brokerage, as: user.to_param) }

      it 'creates note' do
        post url, params: { company_note: { note: 'I like this brokerage' } }
        expect(CompanyNote.last).to have_attributes company: brokerage, note: 'I like this brokerage', user:
      end

      context 'when create fails' do
        before do
          allow_any_instance_of(CompanyNote).to receive(:update).and_return(false)
        end

        it 'responds with unprocessable status' do
          post url, params: { company_note: { note: 'I like this brokerage' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'GET show' do
    let(:url) { brokerage_note_url(brokerage) }

    it_behaves_like 'an authenticated action', :get

    context 'when user is present' do
      let(:user) { create :user, :broker }
      let(:url) { brokerage_note_url(brokerage, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'DELETE destroy' do
    let(:url) { brokerage_note_url(brokerage) }

    it_behaves_like 'an authenticated action', :delete

    context 'when user is present' do
      let(:user) { create :user, :broker }
      let!(:company_note) { CompanyNote.create(company: brokerage, user:, entity_type: 'broker') }
      let(:url) { brokerage_note_url(brokerage, as: user.to_param) }

      it 'removes note' do
        delete url
        expect { company_note.reload }.to raise_error ActiveRecord::RecordNotFound
      end

      it 'redirects to brokerage page' do
        delete url
        expect(response).to redirect_to brokerage_url(brokerage)
      end
    end
  end
end
