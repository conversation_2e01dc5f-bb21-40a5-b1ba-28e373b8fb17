require 'rails_helper'

RSpec.describe BrokerageReviewsController do
  let(:brokerage) { create :company, :broker }

  describe 'GET new' do
    context 'when user is not signed in' do
      let(:password) { Faker::Internet.password(special_characters: true) }
      let(:user) { create :user, :carrier, password: }

      it 'redirects to sign in' do
        get new_brokerage_brokerage_review_url(brokerage)
        expect(response).to redirect_to sign_in_url
      end

      it 'redirects to form after logging in' do
        get new_brokerage_brokerage_review_url(brokerage)
        follow_redirect!
        post session_url, params: { session: { email: user.email, password: } }
        expect(response).to redirect_to new_brokerage_brokerage_review_url(brokerage)
      end
    end

    context 'when user is signed in' do
      context 'when user is a carrier' do
        let(:user) { create :user, :carrier }

        context 'with no existing review' do
          it 'responds with ok' do
            get new_brokerage_brokerage_review_url(brokerage, as: user.to_param)
            expect(response).to have_http_status :ok
          end
        end

        context 'with existing review' do
          let!(:review) { create :brokerage_review, :carrier, company: brokerage, user: }

          it 'redirects to existing review' do
            get new_brokerage_brokerage_review_url(brokerage, as: user.to_param)
            expect(response).to redirect_to edit_brokerage_review_url(review)
          end
        end
      end
    end
  end

  describe 'POST create' do
    context 'when user is not signed in' do
      it 'redirects to sign in' do
        post brokerage_brokerage_reviews_url(brokerage)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is signed in' do
      context 'when user is a carrier' do
        let(:user) { create :user, :carrier }
        let(:pickup_city) { create :city }
        let(:dropoff_city) { create :city }

        let(:review_attributes) do
          attributes_for(:brokerage_review, :carrier, user:).merge(
            company_id: brokerage.id,
            review_lanes_attributes: { 0 => { pickup_city_id: pickup_city.id, dropoff_city_id: dropoff_city.id } }
          )
        end

        context 'with no existing review' do
          it 'redirects to show page' do
            post brokerage_brokerage_reviews_url(brokerage, as: user.to_param),
                 params: { brokerage_review: review_attributes }
            expect(response).to redirect_to brokerage_review_url(BrokerageReview.last)
          end

          it 'saves attributes' do
            post brokerage_brokerage_reviews_url(brokerage, as: user.to_param),
                 params: { brokerage_review: review_attributes }
            expect(BrokerageReview.last).to have_attributes status: 'submitted', title: review_attributes[:title]
          end

          it 'responds with error for incomplete attributes' do
            post brokerage_brokerage_reviews_url(brokerage, as: user.to_param)
            expect(response).to have_http_status :unprocessable_content
          end

          it 'persists utm params' do
            post brokerage_brokerage_reviews_url(brokerage, as: user.to_param),
                 params: { brokerage_review: review_attributes, utm_source: 'facebook', utm_campaign: 'social' }
            expect(BrokerageReview.last.utm_param).to have_attributes source: 'facebook', campaign: 'social'
          end
        end

        context 'with existing review' do
          let!(:review) { create :brokerage_review, :carrier, company: brokerage, user: }

          it 'does not raise error' do
            expect do
              post brokerage_brokerage_reviews_url(brokerage, as: user.to_param), params: { review: review_attributes }
            end.not_to raise_error
          end
        end
      end
    end
  end

  describe 'GET show' do
    let(:user) { create :user, :carrier }
    let(:review) { create :brokerage_review, :carrier, company: brokerage, user: }

    context 'when user is not signed in' do
      it 'redirects to sign in' do
        get brokerage_review_url(review)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is signed in' do
      context 'when viewing own review' do
        it 'responds with ok' do
          get brokerage_review_url(review, as: user.to_param)
          expect(response).to have_http_status :ok
        end
      end

      context "when viewing someone else's review" do
        let(:other) { create :user, :carrier }

        it 'redirects to root' do
          get brokerage_review_url(review, as: other.to_param)
          expect(response).to redirect_to root_url
        end
      end
    end
  end

  describe 'GET edit' do
    let(:user) { create :user, :carrier }
    let(:review) { create :brokerage_review, :carrier, company: brokerage, user: }

    context 'when user is not signed in' do
      it 'redirects to sign in' do
        get edit_brokerage_review_url(review)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is signed in' do
      context 'when review belongs to user' do
        it 'responds with ok' do
          get edit_brokerage_review_url(review, as: user.to_param)
          expect(response).to have_http_status :ok
        end
      end

      context 'when review does not belong to user' do
        let(:other) { create :user, :carrier }

        it 'redirects to root' do
          get edit_brokerage_review_url(review, as: other.to_param)
          expect(response).to redirect_to root_url
        end
      end
    end
  end

  describe 'PUT update' do
    let(:user) { create :user, :carrier }
    let(:review) { create :brokerage_review, :carrier, :approved, company: brokerage, user: }

    context 'when user is not signed in' do
      it 'redirects to sign in' do
        put brokerage_review_url(review)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is signed in' do
      context 'when review belongs to user' do
        it 'redirects to show' do
          put brokerage_review_url(review, as: user.to_param)
          expect(response).to redirect_to brokerage_review_url(review)
        end

        it 'updates the status to submitted' do
          put brokerage_review_url(review, as: user.to_param)
          expect(review.reload).to be_submitted
        end

        it 'responds with error for missing attribute' do
          put brokerage_review_url(review, as: user.to_param), params: { brokerage_review: { body: 'something short' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end

      context 'when review does not belong to user' do
        let(:other) { create :user, :carrier }

        it 'redirects to root' do
          put brokerage_review_url(review, as: other.to_param)
          expect(response).to redirect_to root_url
        end
      end
    end
  end

  describe 'POST autosave' do
    let(:user) { create :user, :carrier }

    context 'when user is not signed in' do
      it 'redirects to sign in' do
        post autosave_brokerage_brokerage_reviews_url(brokerage)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is signed in' do
      it 'responds with ok' do
        post autosave_brokerage_brokerage_reviews_url(brokerage, as: user.to_param),
             params: { brokerage_review: { title: 'Great' } }
        expect(response).to have_http_status :ok
      end

      it 'saves invalid attributes' do
        post autosave_brokerage_brokerage_reviews_url(brokerage, as: user.to_param),
             params: { brokerage_review: { title: 'Great' } }
        expect(BrokerageReview.last).to have_attributes title: 'Great', status: 'pending'
      end

      context 'when save fails' do
        before do
          allow_any_instance_of(BrokerageReview).to receive(:save).and_return false
        end

        it 'responds with error' do
          post autosave_brokerage_brokerage_reviews_url(brokerage, as: user.to_param),
               params: { brokerage_review: { title: 'Great' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end
end
