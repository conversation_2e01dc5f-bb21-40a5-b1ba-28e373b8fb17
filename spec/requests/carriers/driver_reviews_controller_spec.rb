require 'rails_helper'

RSpec.describe Carriers::DriverReviewsController do
  let(:carrier) { create :company }

  describe 'GET show' do
    let(:user) { create :user, :driver }
    let(:review) { create :driver_carrier_review, company: carrier, user: }

    it 'responds with ok' do
      get driver_review_url(review, as: user.to_param)
      expect(response).to have_http_status :ok
    end
  end

  describe 'GET new' do
    context 'when user is not signed in' do
      let(:password) { Faker::Internet.password(special_characters: true) }
      let(:user) { create :user, :driver, password: }

      it 'redirects to sign in' do
        get new_carrier_driver_review_url(carrier)
        expect(response).to redirect_to sign_in_url
      end

      it 'redirects to form after logging in' do
        get new_carrier_driver_review_url(carrier)
        follow_redirect!
        post session_url, params: { session: { email: user.email, password: } }
        expect(response).to redirect_to new_carrier_driver_review_url(carrier)
      end
    end

    context 'when user is signed in' do
      context 'when user is not a driver' do
        let(:user) { create :user, :dispatcher }

        it 'redirects to root url' do
          get new_carrier_driver_review_url(carrier, as: user.to_param)
          expect(response).to redirect_to root_url
        end
      end

      context 'when user is a driver' do
        let(:user) { create :user, :driver }

        context 'with no existing review' do
          it 'responds with ok' do
            get new_carrier_driver_review_url(carrier, as: user.to_param)
            expect(response).to have_http_status :ok
          end
        end

        context 'with existing review' do
          let!(:review) { create :driver_carrier_review, company: carrier, user: }

          it 'redirects to existing review' do
            get new_carrier_driver_review_url(carrier, as: user.to_param)
            expect(response).to redirect_to edit_driver_review_url(review)
          end
        end
      end
    end
  end

  describe 'POST create' do
    context 'when user is not signed in' do
      it 'redirects to sign in' do
        post carrier_driver_reviews_url(carrier)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is signed in' do
      context 'when user is not a broker' do
        let(:user) { create :user, :dispatcher }

        it 'redirects to root url' do
          post carrier_driver_reviews_url(carrier, as: user.to_param)
          expect(response).to redirect_to root_url
        end
      end

      context 'when user is a driver' do
        let(:user) { create :user, :driver }

        let(:review_attributes) do
          attributes_for(:driver_carrier_review).merge(company_id: carrier.id)
        end

        context 'with no existing review' do
          it 'redirects to thank you page' do
            post carrier_driver_reviews_url(carrier, as: user.to_param),
                 params: { driver_carrier_review: review_attributes }
            expect(response).to redirect_to driver_review_url(DriverCarrierReview.last)
          end

          it 'responds with error for incomplete attributes' do
            post carrier_driver_reviews_url(carrier, as: user.to_param)
            expect(response).to have_http_status :unprocessable_content
          end

          it 'persists utm params' do
            post carrier_driver_reviews_url(carrier, as: user.to_param),
                 params: { driver_carrier_review: review_attributes, utm_source: 'facebook', utm_campaign: 'social' }
            expect(DriverCarrierReview.last.utm_param).to have_attributes source: 'facebook', campaign: 'social'
          end
        end

        context 'with existing review' do
          let!(:review) { create :driver_carrier_review, company: carrier, user: }

          it 'does not raise error' do
            expect do
              post carrier_driver_reviews_url(carrier, as: user.to_param),
                   params: { driver_carrier_review: review_attributes }
            end.not_to raise_error
          end
        end
      end
    end
  end

  describe 'GET edit' do
    let(:user) { create :user, :driver }
    let(:review) { create :driver_carrier_review, company: carrier, user: }

    context 'when user is not signed in' do
      it 'redirects to sign in' do
        get edit_driver_review_url(review)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is signed in' do
      context 'when review belongs to user' do
        it 'responds with ok' do
          get edit_driver_review_url(review, as: user.to_param)
          expect(response).to have_http_status :ok
        end
      end

      context 'when review does not belong to user' do
        let(:other) { create :user, :driver }

        it 'redirects to root' do
          get edit_driver_review_url(review, as: other.to_param)
          expect(response).to redirect_to root_url
        end
      end
    end
  end

  describe 'PUT update' do
    let(:user) { create :user, :driver }
    let(:review) { create :driver_carrier_review, :approved, company: carrier, user: }

    context 'when user is not signed in' do
      it 'redirects to sign in' do
        put driver_review_url(review)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is signed in' do
      context 'when review belongs to user' do
        it 'redirects to show' do
          put driver_review_url(review, as: user.to_param)
          expect(response).to redirect_to driver_review_url(review)
        end

        it 'updates the status to submitted' do
          put driver_review_url(review, as: user.to_param)
          expect(review.reload).to be_submitted
        end

        it 'responds with error for missing attribute' do
          put driver_review_url(review, as: user.to_param),
              params: { driver_carrier_review: { body: 'something short' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end

      context 'when review does not belong to user' do
        let(:other) { create :user, :driver }

        it 'redirects to root' do
          put driver_review_url(review, as: other.to_param)
          expect(response).to redirect_to root_url
        end
      end
    end
  end

  describe 'POST autosave' do
    let(:user) { create :user, :driver }
    let(:review) { create :driver_carrier_review, company: carrier, user: }

    context 'when user is not signed in' do
      it 'redirects to sign in' do
        post autosave_carrier_driver_reviews_url(carrier)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is signed in' do
      it 'responds with ok' do
        post autosave_carrier_driver_reviews_url(carrier, as: user.to_param)
        expect(response).to have_http_status :ok
      end

      it 'saves invalid attributes' do
        post autosave_carrier_driver_reviews_url(carrier, as: user.to_param),
             params: { driver_carrier_review: { title: 'boo' } }
        expect(DriverCarrierReview.last).to have_attributes title: 'boo', status: 'pending'
      end

      context 'when save fails' do
        before do
          allow_any_instance_of(DriverCarrierReview).to receive(:save).and_return false
        end

        it 'responds with error' do
          post autosave_carrier_driver_reviews_url(carrier, as: user.to_param),
               params: { driver_carrier_review: { title: 'boo' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end
end
