require 'rails_helper'

RSpec.describe Carriers::NotesController do
  let(:carrier) { create :company, :carrier }

  shared_examples 'an authenticated action' do |method|
    context 'when user is not logged in' do
      it 'redirects to login' do
        public_send method, url
        expect(response).to redirect_to sign_in_url
      end
    end
  end

  describe 'GET new' do
    let(:url) { new_carrier_note_url(carrier) }

    it_behaves_like 'an authenticated action', :get

    context 'when user is signed in' do
      let(:user) { create :user, :broker }
      let(:url) { new_carrier_note_url(carrier, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { carrier_note_url(carrier) }

    it_behaves_like 'an authenticated action', :post

    context 'when user is signed in' do
      let(:user) { create :user, :broker }
      let(:url) { carrier_note_url(carrier, as: user.to_param) }

      it 'creates note' do
        post url, params: { company_note: { note: 'I like this carrier' } }
        expect(CompanyNote.last).to have_attributes company: carrier, note: 'I like this carrier', user:
      end

      context 'when create fails' do
        before do
          allow_any_instance_of(CompanyNote).to receive(:update).and_return(false)
        end

        it 'responds with unprocessable status' do
          post url, params: { company_note: { note: 'I like this carrier' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'GET show' do
    let(:url) { carrier_note_url(carrier) }

    it_behaves_like 'an authenticated action', :get

    context 'when user is present' do
      let(:user) { create :user, :broker }
      let(:url) { carrier_note_url(carrier, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'DELETE destroy' do
    let(:url) { carrier_note_url(carrier) }

    it_behaves_like 'an authenticated action', :delete

    context 'when user is present' do
      let(:user) { create :user, :broker }
      let!(:company_note) { CompanyNote.create(company: carrier, user:, entity_type: 'carrier') }
      let(:url) { carrier_note_url(carrier, as: user.to_param) }

      it 'removes note' do
        delete url
        expect { company_note.reload }.to raise_error ActiveRecord::RecordNotFound
      end

      it 'redirects to carrier page' do
        delete url
        expect(response).to redirect_to carrier_url(carrier)
      end
    end
  end
end
