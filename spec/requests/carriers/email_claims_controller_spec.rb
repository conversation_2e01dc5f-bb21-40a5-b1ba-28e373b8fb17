require 'rails_helper'

RSpec.describe Carriers::EmailClaimsController do
  let(:carrier) { create :company, :carrier }
  let(:user) { create :user, :carrier }

  describe 'GET #show' do
    context 'when user is signed in' do
      it 'redirects to root_url' do
        get carrier_email_claim_path(carrier, carrier.claim_token, as: user.to_param)
        expect(response).to redirect_to root_url
      end
    end

    context 'when token is not valid' do
      it 'redirects to root_url' do
        get carrier_email_claim_path(carrier, SecureRandom.uuid)
        expect(response).to redirect_to root_url
      end
    end

    context 'when user is not signed in' do
      it 'responds with ok status' do
        get carrier_email_claim_path(carrier, carrier.claim_token)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH #update' do
    context 'when user is signed in' do
      it 'redirects to root_url' do
        patch carrier_email_claim_path(carrier, carrier.claim_token, as: user.to_param)
        expect(response).to redirect_to root_url
      end
    end

    context 'when token is not valid' do
      it 'redirects to root_url' do
        patch carrier_email_claim_path(carrier, SecureRandom.uuid)
        expect(response).to redirect_to root_url
      end
    end

    context 'when user is not signed in' do
      let(:password) { Faker::Internet.password(special_characters: true) }

      context 'with valid params' do
        let(:valid_params) do
          { user: { first_name: 'John', last_name: 'Doe', email: '<EMAIL>', password: } }
        end

        it 'signs in the user' do
          patch carrier_email_claim_path(carrier, carrier.claim_token), params: valid_params
          expect(response).to redirect_to root_url
        end

        it 'verifies the user' do
          patch carrier_email_claim_path(carrier, carrier.claim_token), params: valid_params
          expect(User.last).to be_verified
        end
      end

      context 'with invalid params' do
        let(:invalid_params) do
          { user: { email: '<EMAIL>', password: } }
        end

        it 'responds with unprocessable_content status' do
          patch carrier_email_claim_path(carrier, carrier.claim_token), params: invalid_params
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end
end
