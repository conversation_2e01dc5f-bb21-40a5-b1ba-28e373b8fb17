require 'rails_helper'

RSpec.describe UsersController do
  shared_examples 'redirect signed in user' do |method|
    let(:user) { create :user, :broker }

    before do
      get root_url(as: user.to_param)
    end

    it 'redirects to root' do
      public_send method, url
      expect(response).to redirect_to root_url
    end
  end

  describe 'GET new' do
    let(:url) { sign_up_url }

    it_behaves_like 'redirect signed in user', :get

    context 'when logged out' do
      it 'responds with ok status' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { users_url }
    let(:password) { Faker::Internet.password(special_characters: true) }

    it_behaves_like 'redirect signed in user', :post

    context 'when logged out' do
      it 'redirects to verification url' do
        post url,
             params: { user: { first_name: '<PERSON>', last_name: '<PERSON><PERSON>', email: '<EMAIL>', password: } }
        expect(response).to redirect_to user_verification_url(User.last)
      end

      context 'when parameters are missing' do
        it 'responds with unprocessable status' do
          post url,
               params: { user: { first_name: '<PERSON>', last_name: '<PERSON><PERSON>' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'GET create_from_omniauth' do
    let(:url) { '/auth/linkedin/callback' }

    it_behaves_like 'redirect signed in user', :get

    context 'when logged out' do
      around do |example|
        OmniAuth.config.mock_auth[:linkedin] = OmniAuth::AuthHash.new(Faker::Omniauth.linkedin(email: '<EMAIL>'))
        example.run
        OmniAuth.config.mock_auth[:linkedin] = nil
      end

      it 'redirects to verification url' do
        post url
        expect(response).to redirect_to new_user_persona_url(User.last)
      end
    end
  end

  describe 'GET failure_from_omniauth' do
    let(:url) { '/auth/failure' }

    it_behaves_like 'redirect signed in user', :get

    context 'when logged out' do
      it 'redirects to root' do
        get url, params: { message: 'invalid_credentials', strategy: 'linkedin' }
        expect(response).to redirect_to root_url
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { user_url(user) }
    let(:user) { create :user, :broker }

    context 'when logged out' do
      it 'redirects to login' do
        patch url, params: { user: { password: 'john.doe', password_confirmation: 'john.doe' } }
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when logged in' do
      let(:url) { user_url(user, as: user.to_param) }
      let(:password) { Faker::Internet.password(special_characters: true) }

      context 'when parameters are valid' do
        it 'redirects to root' do
          patch url, params: { user: { password:, password_confirmation: password } }
          expect(response).to redirect_to root_url
        end

        it 'updates the password' do
          patch url, params: { user: { password:, password_confirmation: password } }
          expect(user.reload.authenticated?(password)).to be true
        end
      end

      context 'when parameters are missing' do
        it 'redirects to root' do
          patch url, params: { user: { password: } }
          expect(response).to redirect_to root_url
        end

        it 'does not update the password' do
          patch url, params: { user: { password: } }
          expect(user.reload.authenticated?(password)).to be false
        end
      end
    end
  end
end
