require 'rails_helper'

RSpec.describe <PERSON><PERSON><PERSON>roller do
  describe 'GET new' do
    context 'when user is signed in' do
      let(:user) { create :user, :broker }

      it 'redirects to root' do
        get root_url(as: user.to_param)
        get sign_in_url
        expect(response).to redirect_to root_url
      end
    end

    it 'responds with ok' do
      get sign_in_url
      expect(response).to have_http_status :ok
    end
  end

  describe 'POST create' do
    let(:email) { '<EMAIL>' }
    let(:password) { Faker::Internet.password(special_characters: true) }

    context 'when user is not verified' do
      let!(:user) { create :user, :broker, email:, password:, verified: false }

      it 'redirects to user verification' do
        post session_url, params: { session: { email:, password: } }
        expect(response).to redirect_to user_verification_url(user)
      end
    end

    context 'when user persona has not been set' do
      let!(:user) { create :user, email:, password: }

      it 'redirects to complete sign up' do
        post session_url, params: { session: { email:, password: } }
        expect(response).to redirect_to new_user_persona_url(user)
      end
    end

    context 'when credentials are invalid' do
      let!(:user) { create :user, :broker, email:, password: }

      it 'responds with unauthorized' do
        post session_url, params: { session: { email:, password: 'Foobar@123' } }
        expect(response).to have_http_status :unauthorized
      end
    end

    context 'when credentials are valid' do
      let!(:user) { create :user, :broker, email:, password: }

      it 'redirects to root' do
        post session_url, params: { session: { email:, password: } }
        expect(response).to redirect_to root_url
      end

      it 'returns to stored location' do
        get sign_in_url(return_to: page_url('privacy-policy'))
        post session_url, params: { session: { email:, password: } }
        expect(response).to redirect_to page_url('privacy-policy')
      end

      it 'resets remember token', :wisper do
        expect do
          post session_url, params: { session: { email:, password: } }
        end.to(change { user.reload.remember_token })
      end
    end
  end

  describe 'DELETE destroy' do
    let(:user) { create :user, :broker }

    before do
      get root_url(as: user.to_param)
    end

    it 'logs out user' do
      delete sign_out_url
      expect(response).to redirect_to sign_in_url
    end
  end
end
