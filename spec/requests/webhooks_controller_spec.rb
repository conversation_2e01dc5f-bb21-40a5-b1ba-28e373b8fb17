require 'rails_helper'

RSpec.describe WebhooksController do
  describe 'POST create' do
    context 'when handler is not defined' do
      it 'responds with bad request' do
        post '/webhooks/github'
        expect(response).to have_http_status :bad_request
        expect(JSON.parse(response.body)).to eq 'message' => 'Handler for github not found'
      end
    end

    context 'with stripe handler' do
      let(:event_data) do
        JSON.parse(
          Rails.root.join('spec/fixtures/webhooks/stripe/subscriptions/carrier/customer.subscription.created.json').read
        )
      end

      let(:signature) do
        Stripe::Webhook::Signature.compute_signature(
          timestamp, event_data.to_json, CarrierSource::Credentials.lookup(:stripe, :signing_secret)
        )
      end

      let(:headers) do
        { 'Stripe-Signature' => Stripe::Webhook::Signature.generate_header(timestamp, signature) }
      end

      let(:timestamp) { Time.zone.now }

      context 'with missing signature' do
        it 'responds with bad request' do
          post '/webhooks/stripe', params: event_data, as: :json
          expect(response).to have_http_status :bad_request
          expect(JSON.parse(response.body)).to eq 'message' => 'Unable to extract timestamp and signatures from header'
        end
      end

      context 'with invalid signature' do
        let(:timestamp) { Time.zone.local(2022, 1, 1) }

        it 'responds with bad request' do
          post('/webhooks/stripe', params: event_data, headers:, as: :json)
          expect(response).to have_http_status :bad_request
          expect(response.body).to match 'Timestamp outside the tolerance zone'
        end
      end

      context 'with valid signature' do
        it 'responds with created status' do
          post('/webhooks/stripe', params: event_data, headers:, as: :json)
          expect(response).to have_http_status :no_content
        end

        it 'queues up process job' do
          post('/webhooks/stripe', params: event_data, headers:, as: :json)
          expect(IncomingWebhookEvents::ProcessJob.jobs.size).to eq 1
        end

        context 'when event is already processed' do
          before do
            IncomingWebhookEvent.create source: 'stripe', data: event_data, status: 'processed',
                                        external_id: event_data['id']
          end

          it 'does not queue job' do
            post('/webhooks/stripe', params: event_data, headers:, as: :json)
            expect(IncomingWebhookEvents::ProcessJob.jobs.size).to eq 0
          end
        end
      end
    end

    context 'with readme handler' do
      let(:signature) { SecureRandom.hex }
      let(:headers) { { 'readme-signature' => signature } }
      let(:developer_account_user) { create :developer_account_user }
      let(:event_data) { { email: developer_account_user.user.email } }

      context 'with missing signature' do
        it 'responds with bad request' do
          post '/webhooks/readme', params: event_data, as: :json
          expect(response).to have_http_status :unauthorized
          expect(JSON.parse(response.body)).to eq 'message' => 'Missing Signature'
        end
      end

      context 'with invalid signature' do
        before do
          allow(Readme::Webhook).to receive(:verify).and_raise(Readme::InvalidSignatureError)
        end

        it 'responds with bad request' do
          post('/webhooks/readme', params: event_data, headers:, as: :json)
          expect(response).to have_http_status :unauthorized
          expect(JSON.parse(response.body)).to eq 'message' => 'Invalid Signature'
        end
      end

      context 'with valid signature' do
        before do
          allow(Readme::Webhook).to receive(:verify)
        end

        it 'responds with ok status' do
          post('/webhooks/readme', params: event_data, headers:, as: :json)
          expect(response).to have_http_status :ok
        end
      end
    end

    context 'with simple_notification_service handler' do
      let(:event_data) do
        Rails.root.join('spec/fixtures/webhooks/simple_notification_service/notification.json').read
      end

      let(:headers) { { 'Content-Type' => 'text/plain; charset=UTF-8' } }

      it 'responds with created status' do
        post '/webhooks/simple_notification_service', headers:, params: event_data
        expect(response).to have_http_status :no_content
      end

      it 'creates a new record' do
        post '/webhooks/simple_notification_service', headers:, params: event_data
        event = IncomingWebhookEvent.last
        expect(event).to(
          have_attributes(
            source: 'simple_notification_service',
            data: hash_including('MessageId' => 'b8976ecf-2010-560a-8f04-0e93f41a9a68'),
            external_id: 'b8976ecf-2010-560a-8f04-0e93f41a9a68'
          )
        )
      end

      it 'creates a new email feedback record' do
        post '/webhooks/simple_notification_service', headers:, params: event_data
        IncomingWebhookEvents::ProcessJob.drain
        expect(EmailFeedback.last).to have_attributes email: '<EMAIL>', feedback_type: 'bounce'
      end
    end
  end
end
