require 'rails_helper'

RSpec.describe WriteReviewController do
  describe 'GET search', :elasticsearch do
    let!(:company) { create :company, :carrier, :with_city, :with_authority, legal_name: 'Swift Truck Line LLC' }

    before do
      Carrier.es.import force: true, query: -> { es_import_query }, refresh: true
      Brokerage.es.import force: true, query: -> { es_import_query }, refresh: true
    end

    context 'with no query' do
      it 'responds with ok status' do
        get write_review_url
        expect(response).to have_http_status :ok
      end

      it 'does not return any carriers' do
        get write_review_url
        expect(response.body).not_to include 'Swift Truck Line LLC'
      end
    end

    context 'with query' do
      it 'responds with ok status' do
        get write_review_url(query: 'swift t')
        expect(response).to have_http_status :ok
      end

      it 'renders matching carrier' do
        get write_review_url(query: 'swift t')
        expect(response.body).to include 'Swift Truck Line LLC'
      end
    end
  end
end
