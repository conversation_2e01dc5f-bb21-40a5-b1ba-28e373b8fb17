require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::DeveloperAccountsController do
  describe 'GET index' do
    let(:url) { admin_developer_accounts_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :developer_account
      end

      it 'responds with ok status' do
        get admin_developer_accounts_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET new' do
    let(:url) { new_admin_developer_account_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get new_admin_developer_account_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_developer_account_url(developer_account) }
    let(:developer_account) { create :developer_account }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :developer_account_permission, developer_account: developer_account
        create :developer_account_user, developer_account: developer_account, user: user
      end

      it 'responds with ok status' do
        get admin_developer_account_url(developer_account, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { admin_developer_accounts_url }

    it_behaves_like 'an activeadmin controller action', :post

    context 'when logged in user has access' do
      let(:valid_params) { { developer_account: { name: 'New Account' } } }

      include_context 'with super admin user'

      it 'creates a new developer account' do
        expect do
          post admin_developer_accounts_url(as: user.to_param), params: valid_params
        end.to change(DeveloperAccount, :count).by(1)
      end
    end
  end
end
