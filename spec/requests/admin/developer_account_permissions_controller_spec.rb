require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::DeveloperAccountPermissionsController do
  describe 'GET index' do
    let(:url) { admin_developer_account_permissions_url }
    let(:developer_account) { create :developer_account }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :developer_account_permission, developer_account:
      end

      it 'responds with ok status' do
        get admin_developer_account_permissions_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET new' do
    let(:url) { new_admin_developer_account_permission_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get new_admin_developer_account_permission_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_developer_account_permission_url(developer_account_permission) }
    let(:developer_account_permission) { create :developer_account_permission }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get admin_developer_account_permission_url(developer_account_permission, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end
end
