require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe Admin::DeveloperAccountUsersController do
  describe 'GET index' do
    let(:url) { admin_developer_account_users_url }
    let(:developer_account) { create :developer_account }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      before do
        create :developer_account_user, developer_account: developer_account, user: user
      end

      it 'responds with ok status' do
        get admin_developer_account_users_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET new' do
    let(:url) { new_admin_developer_account_user_url }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get new_admin_developer_account_user_url(as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET show' do
    let(:url) { admin_developer_account_user_url(developer_account_user) }
    let(:developer_account_user) { create :developer_account_user }

    it_behaves_like 'an activeadmin controller action', :get

    context 'when logged in user has access' do
      include_context 'with super admin user'

      it 'responds with ok status' do
        get admin_developer_account_user_url(developer_account_user, as: user.to_param)
        expect(response).to have_http_status :ok
      end
    end
  end
end
