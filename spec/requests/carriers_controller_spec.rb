require 'rails_helper'

RSpec.describe CarriersController do
  describe 'GET claim', :elasticsearch do
    let!(:company) { create :company, :carrier, :with_city }

    before do
      Carrier.es.import force: true, query: -> { es_import_query }, refresh: true
    end

    context 'with no query' do
      it 'responds with ok status' do
        get claim_carriers_url
        expect(response).to have_http_status :ok
      end
    end

    it 'responds with ok status' do
      get claim_carriers_url(query: company.legal_name)
      expect(response).to have_http_status :ok
    end
  end

  describe 'GET review' do
    let(:company) { create :company, :carrier, :with_city, :with_authority }

    context 'with logged in user' do
      let(:user) { create :user, :broker }

      it 'redirects to review page' do
        get review_carrier_url(company, as: user.to_param)
        expect(response).to redirect_to new_carrier_review_url(company)
      end
    end

    context 'with logged out user' do
      it 'responds with ok status' do
        get review_carrier_url(company)
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET related', :elasticsearch do
    let(:census) { create :census, carrier_mailing_street: '123 Main St', carrier_mailing_zip: '12345' }
    let!(:company) { create :company, :carrier, :with_city, :with_authority, census: }

    before do
      cen = create :census, carrier_mailing_street: '123 Main St', carrier_mailing_zip: '12345'
      create :company, :carrier, :with_city, :with_authority, census: cen
      Carrier.es.import force: true, query: -> { es_import_query }, refresh: true
    end

    it 'responds with ok status' do
      get related_carrier_url(company)
      expect(response).to have_http_status :ok
    end
  end

  describe 'GET show', :elasticsearch do
    let(:carrier_profile) { create :carrier_profile, company: }

    let(:company) do
      create :company, :carrier, :with_city, :with_authority, legal_name: 'Putnik Express', **carrier_attributes
    end

    let!(:carrier) { carrier_profile.carrier }

    let(:carrier_attributes) { {} }

    before { Carrier.es.import force: true, query: -> { es_import_query }, refresh: true }

    context 'with legacy route' do
      it 'redirects' do
        get "/carrier/Putnik-Express-#{carrier.operating_authorities.first.docket_number}"
        expect(response).to redirect_to carrier_url(carrier)
      end
    end

    context 'when carrier slug is stale' do
      before do
        company.update(legal_name: 'Putnik Express Inc')
      end

      it 'redirects to current slug' do
        get '/carriers/putnik-express'
        expect(response).to redirect_to '/carriers/putnik-express-inc'
        expect(response).to have_http_status :moved_permanently
      end
    end

    context 'when carrier profile is hidden' do
      let(:carrier_attributes) { { hidden: true } }

      it 'renders not found' do
        expect do
          get carrier_url(carrier)
        end.to raise_error ActiveRecord::RecordNotFound
      end
    end

    context 'when carrier is not associated to city' do
      let(:company) { create :company, :carrier, :with_authority, legal_name: 'Putnik Express', city: nil }

      it 'renders not found' do
        expect do
          get carrier_url(carrier)
        end.to raise_error ActiveRecord::RecordNotFound
      end
    end

    context 'when carrier is inactive' do
      let(:company) { create :company, :carrier, :with_city, legal_name: 'Putnik Express', census: nil, dot_number: 1 }

      it 'renders with ok status' do
        get carrier_url(carrier)
        expect(response).to have_http_status :ok
      end
    end

    context 'when carrier is associated to city' do
      it 'renders success response' do
        get carrier_url(carrier)
        expect(response).to have_http_status :ok
      end

      context 'when authority is not active' do
        let(:company) { create :company, :carrier, :with_city, legal_name: 'Putnik Express' }

        before do
          create :operating_authority, company:, common_authority: 'none', contract_authority: 'none'
        end

        it 'renders with ok status' do
          get carrier_url(carrier)
          expect(response).to have_http_status :ok
        end
      end

      context 'when logged in' do
        let(:user) { create :user, :broker }

        it 'records in recently viewed' do
          expect { get carrier_url(carrier, as: user.to_param) }.to change(RecentlyViewed, :count).by(1)
        end
      end
    end

    context 'with utm params' do
      it 'tracks utm params' do
        get carrier_url(carrier, utm_source: 'email_signature', utm_campaign: carrier.to_param),
            headers: { 'HTTP_USER_AGENT' => Faker::Internet.user_agent }
        expect(Analytics::Event.last).to have_attributes name: 'Email Signature Clicked'
      end
    end
  end

  describe 'GET availability', :elasticsearch do
    let!(:carrier_profile) { create :carrier_profile, company: }
    let(:company) { create :company, :carrier, :with_city }

    before do
      create(:carrier_availability, company:)
      CarrierAvailability.es.import force: true, query: -> { es_import_query }, refresh: true
    end

    it 'responds with ok status' do
      get availability_carrier_url(company)
      expect(response).to have_http_status :ok
    end
  end
end
