require 'rails_helper'

RSpec.describe CarrierNetworkBuilderLaneEntitiesController do
  let(:user) { create :user, :shipper }
  let(:carrier_network_builder) { create :carrier_network_builder, user: }
  let!(:lane) { create :carrier_network_builder_lane, carrier_network_builder: }

  describe 'GET index' do
    let(:url) { carrier_network_builder_lane_entities_path(carrier_network_builder.uuid, lane) }

    context 'when refresh is pending' do
      it 'returns no content' do
        get url, headers: { 'Accept' => 'text/vnd.turbo-stream.html' }
        expect(response).to have_http_status :no_content
      end
    end

    context 'when refresh is complete' do
      before do
        lane.complete_refresh!
      end

      it 'renders the index page' do
        get url, headers: { 'Accept' => 'text/vnd.turbo-stream.html' }
        expect(response).to have_http_status :ok
      end
    end
  end
end
