require 'rails_helper'

RSpec.describe TruckingCompanies::LocationsController, :elasticsearch do
  let(:city) { create :city, name: 'Buffalo', state_code: 'NY', latitude: 42.9, longitude: -78.9 }
  let(:postal_code) { create :postal_code, code: '14201' }

  before do
    create(:company, :carrier, :with_city, :with_authority, city:, postal_code:).then do |company|
      create(:carrier_profile, company:).then do |profile|
        create(:access_package, :carrier, resource: profile)
      end
    end

    Carrier.es.import force: true, refresh: true, query: -> { es_import_query }
    CarrierCity.refresh
  end

  describe 'GET index' do
    it 'returns success status' do
      get trucking_companies_url
      expect(response).to have_http_status :ok
    end
  end

  describe 'GET country' do
    before { CarrierCity.refresh }

    it 'returns success status' do
      get country_trucking_companies_url('united-states')
      expect(response).to have_http_status :ok
    end

    it 'redirects to canonical slug' do
      get country_trucking_companies_url('us')
      expect(response).to redirect_to country_trucking_companies_url('united-states')
    end

    context 'when country is invalid' do
      it 'responds with not found' do
        expect { get country_trucking_companies_url('country') }.to raise_error ActionController::RoutingError
      end
    end

    context 'when pagination is out of bounds' do
      it 'redirects to canonical page' do
        get country_trucking_companies_url('united-states', page: 30)
        expect(response).to redirect_to country_trucking_companies_url('united-states')
      end
    end
  end

  describe 'GET state' do
    before { CarrierCity.refresh }

    it 'returns success status' do
      get state_trucking_companies_url('united-states', 'new-york')
      expect(response).to have_http_status :ok
    end

    it 'redirects to canonical slug' do
      get state_trucking_companies_url('us', 'new-york')
      expect(response).to redirect_to state_trucking_companies_url('united-states', 'new-york')
    end

    context 'when state is invalid' do
      it 'responds with not found' do
        expect do
          get state_trucking_companies_url('united-states', 'british-columbia')
        end.to raise_error ActiveRecord::RecordNotFound
      end
    end

    context 'when pagination is out of bounds' do
      it 'redirects to canonical page' do
        get state_trucking_companies_url('united-states', 'new-york', page: 30)
        expect(response).to redirect_to state_trucking_companies_url('united-states', 'new-york')
      end
    end
  end

  describe 'GET city' do
    it 'returns success status' do
      get city_trucking_companies_url('united-states', 'new-york', 'buffalo')
      expect(response).to have_http_status :ok
    end

    it 'redirects to canonical slug' do
      get city_trucking_companies_url('us', 'new-york', 'buffalo')
      expect(response).to redirect_to city_trucking_companies_url('united-states', 'new-york', 'buffalo')
    end

    context 'when pagination is out of bounds' do
      it 'redirects to canonical page' do
        get city_trucking_companies_url('united-states', 'new-york', 'buffalo', page: 30)
        expect(response).to redirect_to city_trucking_companies_url('united-states', 'new-york', 'buffalo')
      end
    end

    context 'when there are no matching carriers' do
      context 'when there is a nearby city' do
        let(:woodlawn) { create :city, name: 'Woodlawn', state_code: 'NY', latitude: 42.8, longitude: -78.8 }

        it 'redirects to nearest city' do
          get city_trucking_companies_url(*woodlawn.path)
          expect(response).to redirect_to city_trucking_companies_url(*city.path)
        end
      end

      context 'when there is no nearby city' do
        let(:new_york) { create :city, name: 'New York', state_code: 'NY', latitude: 40.7, longitude: -74.0 }

        it 'returns success status' do
          get city_trucking_companies_url(*new_york.path)
          expect(response).to have_http_status :ok
        end
      end
    end
  end
end
