require 'rails_helper'

RSpec.describe TruckingCompanies::SearchController do
  describe 'GET #show', :elasticsearch do
    let!(:carrier) { create :company, :carrier, :with_authority, :with_city, legal_name: 'Putnik Express' }
    let!(:carrier_profile) { create :carrier_profile, company: carrier }

    let!(:brokerage) { create :company, :broker, :with_city, legal_name: 'Putnik LLC' }
    let!(:brokerage_profile) { create :brokerage_profile, company: brokerage }

    before do
      Brokerage.es.import query: -> { es_import_query }, force: true, refresh: true
      Carrier.es.import query: -> { es_import_query }, force: true, refresh: true
    end

    context 'with html format' do
      it 'returns http success' do
        get search_trucking_companies_url(carrier: { query: 'putnik' })
        expect(response).to have_http_status(:ok)
      end
    end

    context 'with json format' do
      it 'returns http success' do
        get search_trucking_companies_url(carrier: { query: 'putnik' }, format: :json)
        expect(response).to have_http_status(:ok)
      end
    end
  end
end
