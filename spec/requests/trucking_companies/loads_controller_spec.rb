require 'rails_helper'

RSpec.describe TruckingCompanies::LoadsController, :elasticsearch do
  let(:record) { truck_types(:flatbed) }
  let(:city) { create :city, name: 'Buffalo', state_code: 'NY', latitude: 42.9, longitude: -78.9 }
  let(:postal_code) { create :postal_code, code: '14201' }
  let!(:carrier_profile) { create :carrier_profile, company: }
  let(:company) { create :company, :carrier, :with_city, :with_authority, postal_code:, city: }

  before do
    CarriersTruckType.create(company:, truck_type: record)
    CityTruckType.refresh
    Carrier.es.import force: true, refresh: true, query: -> { es_import_query }
  end

  describe 'GET index' do
    it 'returns success status' do
      get load_trucking_companies_url(record)
      expect(response).to have_http_status :ok
    end

    context 'when old slug for load is used' do
      before do
        FriendlyId::Slug.create(sluggable: record, slug: 'flatbed')
        record.update!(name: 'Flat Bed')
      end

      it 'redirects to canonical slug' do
        get load_trucking_companies_url('flatbed')
        expect(response).to redirect_to load_trucking_companies_url('flat-bed')
      end
    end

    context 'when pagination is out of bounds' do
      it 'redirects to canonical page' do
        get load_trucking_companies_url(record, page: 30)
        expect(response).to redirect_to load_trucking_companies_url(record)
      end
    end
  end

  describe 'GET country' do
    it 'returns success status' do
      get country_load_trucking_companies_url(record, 'united-states')
      expect(response).to have_http_status :ok
    end

    it 'redirects to canonical slug' do
      get country_load_trucking_companies_url(record, 'us')
      expect(response).to redirect_to country_load_trucking_companies_url(record, 'united-states')
    end

    context 'when country is invalid' do
      it 'responds with not found' do
        expect do
          get country_load_trucking_companies_url(record, 'country')
        end.to raise_error ActiveRecord::RecordNotFound
      end
    end

    context 'when pagination is out of bounds' do
      it 'redirects to canonical page' do
        get country_load_trucking_companies_url(record, 'united-states', page: 30)
        expect(response).to redirect_to country_load_trucking_companies_url(record, 'united-states')
      end
    end
  end

  describe 'GET state' do
    it 'returns success status' do
      get state_load_trucking_companies_url(record, 'united-states', 'new-york')
      expect(response).to have_http_status :ok
    end

    it 'redirects to canonical slug' do
      get state_load_trucking_companies_url(record, 'us', 'new-york')
      expect(response).to redirect_to state_load_trucking_companies_url(record, 'united-states', 'new-york')
    end

    context 'when state is invalid' do
      it 'responds with not found' do
        expect do
          get state_load_trucking_companies_url(record, 'united-states', 'british-columbia')
        end.to raise_error ActiveRecord::RecordNotFound
      end
    end

    context 'when pagination is out of bounds' do
      it 'redirects to canonical page' do
        get state_load_trucking_companies_url(record, *city.state.path, page: 30)
        expect(response).to redirect_to state_load_trucking_companies_url(record, *city.state.path)
      end
    end
  end

  describe 'GET city' do
    it 'returns success status' do
      get city_load_trucking_companies_url(record, *city.path)
      expect(response).to have_http_status :ok
    end

    it 'redirects to canonical slug' do
      get city_load_trucking_companies_url(record, 'us', 'new-york', 'buffalo')
      expect(response).to redirect_to city_load_trucking_companies_url(record, *city.path)
    end

    context 'when pagination is out of bounds' do
      it 'redirects to canonical page' do
        get city_load_trucking_companies_url(record, *city.path, page: 30)
        expect(response).to redirect_to city_load_trucking_companies_url(record, *city.path)
      end
    end

    context 'when there are no matching carriers' do
      context 'when there is a nearby city' do
        let(:woodlawn) { create :city, name: 'Woodlawn', state_code: 'NY', latitude: 42.8, longitude: -78.8 }

        it 'redirects to nearest city' do
          get city_load_trucking_companies_url(record, *woodlawn.path)
          expect(response).to redirect_to city_load_trucking_companies_url(record, *city.path)
        end
      end

      context 'when there is no nearby city' do
        let(:new_york) { create :city, name: 'New York', state_code: 'NY', latitude: 40.7, longitude: -74.0 }

        it 'returns success status' do
          get city_load_trucking_companies_url(record, *new_york.path)
          expect(response).to have_http_status :ok
        end
      end
    end
  end
end
