require 'rails_helper'

RSpec.describe ReviewsController do
  let(:company) { create :company, :carrier }
  let(:carrier) { company.as_entity(:carrier) }

  describe 'GET new' do
    context 'when user is not signed in' do
      let(:password) { Faker::Internet.password(special_characters: true) }
      let(:user) { create :user, :broker, password: }

      it 'redirects to sign in' do
        get new_carrier_review_url(carrier)
        expect(response).to redirect_to sign_in_url
      end

      it 'redirects to form after logging in' do
        get new_carrier_review_url(carrier)
        follow_redirect!
        post session_url, params: { session: { email: user.email, password: } }
        expect(response).to redirect_to new_carrier_review_url(carrier)
      end
    end

    context 'when user is signed in' do
      context 'when user is a broker' do
        let(:user) { create :user, :broker }

        context 'with no existing review' do
          it 'responds with ok' do
            get new_carrier_review_url(carrier, as: user.to_param)
            expect(response).to have_http_status :ok
          end
        end

        context 'with existing review' do
          let!(:review) { create :review, company:, user: }

          it 'redirects to existing review' do
            get new_carrier_review_url(carrier, as: user.to_param)
            expect(response).to redirect_to edit_review_url(review)
          end
        end
      end
    end
  end

  describe 'POST create' do
    context 'when user is not signed in' do
      it 'redirects to sign in' do
        post carrier_reviews_url(carrier)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is signed in' do
      context 'when user is a broker' do
        let(:user) { create :user, :broker }
        let(:pickup_city) { create :city }
        let(:dropoff_city) { create :city }

        let(:review_attributes) do
          attributes_for(:review, user:).merge(
            company_id: company.id,
            review_lanes_attributes: { 0 => { pickup_city_id: pickup_city.id, dropoff_city_id: dropoff_city.id } }
          )
        end

        context 'with no existing review' do
          it 'redirects to show page' do
            post carrier_reviews_url(carrier, as: user.to_param), params: { review: review_attributes }
            expect(response).to redirect_to review_url(Review.last)
          end

          it 'saves attributes' do
            post carrier_reviews_url(carrier, as: user.to_param), params: { review: review_attributes }
            expect(Review.last).to have_attributes status: 'submitted', title: review_attributes[:title]
          end

          it 'responds with error for incomplete attributes' do
            post carrier_reviews_url(carrier, as: user.to_param)
            expect(response).to have_http_status :unprocessable_content
          end

          it 'persists utm params' do
            post carrier_reviews_url(carrier, as: user.to_param),
                 params: { review: review_attributes, utm_source: 'facebook', utm_campaign: 'social' }
            expect(Review.last.utm_param).to have_attributes source: 'facebook', campaign: 'social'
          end
        end

        context 'with existing review' do
          let!(:review) { create :review, company:, user: }

          it 'does not raise error' do
            expect do
              post carrier_reviews_url(carrier, as: user.to_param), params: { review: review_attributes }
            end.not_to raise_error
          end
        end
      end
    end
  end

  describe 'GET show' do
    let(:user) { create :user, :broker }
    let(:review) { create :review, company:, user: }

    context 'when user is not signed in' do
      it 'redirects to sign in' do
        get review_url(review)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is signed in' do
      context 'when viewing own review' do
        it 'responds with ok' do
          get review_url(review, as: user.to_param)
          expect(response).to have_http_status :ok
        end
      end

      context "when viewing someone else's review" do
        let(:other) { create :user, :broker }

        it 'redirects to root' do
          get review_url(review, as: other.to_param)
          expect(response).to redirect_to root_url
        end
      end
    end
  end

  describe 'GET edit' do
    let(:user) { create :user, :broker }
    let(:review) { create :review, company:, user: }

    context 'when user is not signed in' do
      it 'redirects to sign in' do
        get edit_review_url(review)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is signed in' do
      context 'when review belongs to user' do
        it 'responds with ok' do
          get edit_review_url(review, as: user.to_param)
          expect(response).to have_http_status :ok
        end
      end

      context 'when review does not belong to user' do
        let(:other) { create :user, :broker }

        it 'redirects to root' do
          get edit_review_url(review, as: other.to_param)
          expect(response).to redirect_to root_url
        end
      end
    end
  end

  describe 'PUT update' do
    let(:user) { create :user, :broker }
    let(:review) { create :review, :approved, company:, user: }

    context 'when user is not signed in' do
      it 'redirects to sign in' do
        put review_url(review)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is signed in' do
      context 'when review belongs to user' do
        it 'redirects to show' do
          put review_url(review, as: user.to_param)
          expect(response).to redirect_to review_url(review)
        end

        it 'updates the status to submitted' do
          put review_url(review, as: user.to_param)
          expect(review.reload).to be_submitted
        end

        it 'responds with error for missing attribute' do
          put review_url(review, as: user.to_param), params: { review: { body: 'something short' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end

      context 'when review does not belong to user' do
        let(:other) { create :user, :broker }

        it 'redirects to root' do
          put review_url(review, as: other.to_param)
          expect(response).to redirect_to root_url
        end
      end
    end
  end

  describe 'POST autosave' do
    let(:user) { create :user, :broker }

    context 'when user is not signed in' do
      it 'redirects to sign in' do
        post autosave_carrier_reviews_url(carrier)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is signed in' do
      it 'responds with ok' do
        post autosave_carrier_reviews_url(carrier, as: user.to_param), params: { review: { title: 'Great' } }
        expect(response).to have_http_status :ok
      end

      it 'saves invalid attributes' do
        post autosave_carrier_reviews_url(carrier, as: user.to_param), params: { review: { title: 'Great' } }
        expect(Review.last).to have_attributes title: 'Great', status: 'pending'
      end

      context 'when save fails' do
        before do
          allow_any_instance_of(Review).to receive(:save).and_return false
        end

        it 'responds with error' do
          post autosave_carrier_reviews_url(carrier, as: user.to_param), params: { review: { title: 'Great' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end
end
