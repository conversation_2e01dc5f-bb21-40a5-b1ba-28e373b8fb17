require 'rails_helper'

RSpec.describe WidgetsController do
  describe 'GET #show' do
    shared_examples 'a renderable widget' do
      it 'responds with ok status' do
        get widget_url(widget.uuid)
        expect(response).to have_http_status(:ok)
      end

      it 'unsets the X-Frame-Options header' do
        get widget_url(widget.uuid)
        expect(response.headers['X-Frame-Options']).to be_nil
      end

      it 'sets the Content-Security-Policy header' do
        get widget_url(widget.uuid)
        expect(response.headers['Content-Security-Policy']).to eq('frame-ancestors *')
      end
    end

    context 'with carrier review widget' do
      let(:widget) { create :widget, :carrier, widget_type: 'review' }

      before do
        create :review, :approved, company: widget.company
      end

      it_behaves_like 'a renderable widget'
    end

    context 'with brokerage review widget' do
      let(:widget) { create :widget, :broker, widget_type: 'review' }

      before do
        create :brokerage_review, :carrier, :approved, company: widget.company
      end

      it_behaves_like 'a renderable widget'
    end
  end

  describe 'GET #embed_html' do
    let(:widget) { create :widget, :carrier, widget_type: 'review' }

    it 'responds with ok status' do
      get embed_html_widget_url(widget.uuid)
      expect(response).to have_http_status(:ok)
    end
  end

  describe 'GET #image' do
    let(:widget) { create :widget, :carrier, widget_type: 'collect_review' }

    context 'when attached' do
      before do
        widget.image.attach(io: fixture_file_upload('logo.png', 'image/png'), filename: 'logo.png')
      end

      it 'redirects to blob' do
        get image_widget_url(widget.uuid)
        expect(response).to redirect_to rails_blob_url(widget.image)
      end
    end

    context 'when not attached' do
      it 'returns a not found response' do
        get image_widget_url(widget.uuid)
        expect(response).to have_http_status :not_found
      end
    end
  end
end
