RSpec.shared_context 'with verified carrier profile user' do
  let!(:carrier_profile) { create :carrier_profile, company: }
  let(:company) { create :company, :carrier, :with_city, **company_attributes }
  let(:carrier) { carrier_profile.carrier }
  let(:profile_user) { create :carrier_profile_user, :verified, carrier_profile: }
  let(:user) { profile_user.user }
  let(:company_attributes) { {} }
end
