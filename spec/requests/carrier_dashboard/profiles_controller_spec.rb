require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe CarrierDashboard::ProfilesController do
  include_context 'with verified carrier profile user'

  describe 'GET show' do
    let(:url) { carrier_dashboard_profile_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_profile_url(company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST update' do
    let(:url) { carrier_dashboard_profile_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :patch

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_profile_url(company, as: user.to_param) }

      it 'updates attributes' do
        patch url, params: { carrier_profile: { bio: 'We are the best!' } }
        expect(carrier_profile.reload).to have_attributes bio: 'We are the best!'
      end

      it 'creates an audit record', :audited do
        patch url, params: { carrier_profile: { bio: 'We are the best!' } }
        expect(carrier_profile.audits.last).to(
          have_attributes(
            user:, audited_changes: hash_including('bio' => [carrier_profile.bio, 'We are the best!'])
          )
        )
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(CarrierProfile).to receive(:update).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url, params: { carrier_profile: { bio: 'We are the best!' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end
end
