require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe CarrierDashboard::AssetsController do
  include_context 'with verified carrier profile user'

  describe 'GET index' do
    let(:url) { carrier_dashboard_assets_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_assets_url(company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET edit' do
    let(:asset) { CarrierProfileAsset.create(carrier_profile:, file: fixture_file_upload('logo.png', 'image/png')) }
    let(:url) { edit_carrier_dashboard_asset_url(company, asset) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { edit_carrier_dashboard_asset_url(company, asset, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { carrier_dashboard_assets_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :post

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_assets_url(company, as: user.to_param) }

      context 'when asset is created' do
        it 'redirects to index' do
          post url, params: { carrier_profile_asset: { file: fixture_file_upload('logo.png', 'image/png') } }
          expect(response).to redirect_to carrier_dashboard_assets_url(company)
        end
      end

      context 'when asset creation fails' do
        it 'responds with unprocessable entity' do
          post url, params: { carrier_profile_asset: { file: nil } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'PATCH update' do
    let(:asset) { CarrierProfileAsset.create(carrier_profile:, file: fixture_file_upload('logo.png', 'image/png')) }
    let(:url) { carrier_dashboard_asset_url(company, asset) }

    it_behaves_like 'a carrier dashboard controller action', :patch

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_asset_url(company, asset, as: user.to_param) }

      it 'updates attributes' do
        patch url, params: { carrier_profile_asset: { title: 'Trip to Chicago' } }
        expect(asset.reload.title).to eq 'Trip to Chicago'
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(CarrierProfileAsset).to receive(:update).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url, params: { carrier_profile_asset: { title: 'Trip to Chicago' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'DELETE destroy' do
    let(:asset) { CarrierProfileAsset.create(carrier_profile:, file: fixture_file_upload('logo.png', 'image/png')) }
    let(:url) { carrier_dashboard_asset_url(company, asset) }

    it_behaves_like 'a carrier dashboard controller action', :delete

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_asset_url(company, asset, as: user.to_param) }

      context 'when destroy succeeds' do
        it 'redirects to index' do
          delete url
          expect(response).to redirect_to carrier_dashboard_assets_url(company)
        end
      end

      context 'when destroy fails' do
        before do
          allow_any_instance_of(CarrierProfileAsset).to receive(:destroy).and_return(false)
        end

        it 'responds with unprocessable status' do
          delete url
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end
end
