require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe CarrierDashboard::DriverJobsController do
  include_context 'with verified carrier profile user'

  describe 'GET index' do
    let(:url) { carrier_dashboard_driver_jobs_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_driver_jobs_url(company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET new' do
    let(:url) { new_carrier_dashboard_driver_job_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { new_carrier_dashboard_driver_job_url(company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET edit' do
    let(:driver_job) { create :driver_job, company: }
    let(:url) { edit_carrier_dashboard_driver_job_url(company, driver_job) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { edit_carrier_dashboard_driver_job_url(company, driver_job, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { carrier_dashboard_driver_jobs_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :post

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_driver_jobs_url(company, as: user.to_param) }

      context 'when job is created' do
        it 'redirects to index' do
          post url, params: { driver_job: attributes_for(:driver_job) }
          expect(response).to redirect_to carrier_dashboard_driver_jobs_url(company)
        end
      end

      context 'when job creation fails' do
        it 'responds with unprocessable entity' do
          post url, params: { driver_job: { title: nil } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'PATCH update' do
    let(:driver_job) { create :driver_job, company: }
    let(:url) { carrier_dashboard_driver_job_url(company, driver_job) }

    it_behaves_like 'a carrier dashboard controller action', :patch

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_driver_job_url(company, driver_job, as: user.to_param) }

      it 'updates attributes' do
        patch url, params: { driver_job: { title: 'Full Time Driver' } }
        expect(driver_job.reload.title).to eq 'Full Time Driver'
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(DriverJob).to receive(:update).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url, params: { driver_job: { title: 'Full Time Driver' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'DELETE destroy' do
    let(:driver_job) { create :driver_job, company: }
    let(:url) { carrier_dashboard_driver_job_url(company, driver_job) }

    it_behaves_like 'a carrier dashboard controller action', :delete

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_driver_job_url(company, driver_job, as: user.to_param) }

      it 'destroys the job' do
        delete url
        expect(DriverJob.exists?(driver_job.id)).to be false
      end

      it 'redirects to index' do
        delete url
        expect(response).to redirect_to carrier_dashboard_driver_jobs_url(company)
      end
    end
  end
end
