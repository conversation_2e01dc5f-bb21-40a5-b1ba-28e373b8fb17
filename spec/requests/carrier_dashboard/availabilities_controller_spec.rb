require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe CarrierDashboard::AvailabilitiesController do
  include_context 'with verified carrier profile user'

  describe 'GET #index' do
    let(:url) { carrier_dashboard_availabilities_url(company) }

    before do
      create :carrier_availability, company:
    end

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_availabilities_url(company, as: user.to_param) }

      it 'returns a success response' do
        get url
        expect(response).to be_successful
      end
    end
  end

  describe 'GET #new' do
    let(:url) { new_carrier_dashboard_availability_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { new_carrier_dashboard_availability_url(company, as: user.to_param) }

      it 'returns a success response' do
        get url
        expect(response).to be_successful
      end
    end
  end

  describe 'GET #edit' do
    let(:availability) { create :carrier_availability, company: }
    let(:url) { edit_carrier_dashboard_availability_url(company, availability) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { edit_carrier_dashboard_availability_url(company, availability, as: user.to_param) }

      it 'returns a success response' do
        get url
        expect(response).to be_successful
      end
    end
  end

  describe 'POST #create' do
    let(:url) { carrier_dashboard_availabilities_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :post

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_availabilities_url(company, as: user.to_param) }

      context 'with valid params' do
        let(:attributes) { attributes_for(:carrier_availability) }

        it 'creates a new CarrierAvailability' do
          expect do
            post url, params: { carrier_availability: attributes }
          end.to change(CarrierAvailability, :count).by(1)
        end

        it 'redirects to the created index page' do
          post url, params: { carrier_availability: attributes }
          expect(response).to redirect_to(carrier_dashboard_availabilities_url)
        end
      end

      context 'with invalid params' do
        let(:attributes) { attributes_for(:carrier_availability, truck_type_id: nil) }

        it 'does not create a new CarrierAvailability' do
          expect do
            post url, params: { carrier_availability: attributes }
          end.not_to change(CarrierAvailability, :count)
        end

        it 'responds with unprocessable entity' do
          post url, params: { carrier_availability: attributes }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'PATCH #update' do
    let(:availability) { create :carrier_availability, company: }
    let(:url) { carrier_dashboard_availability_url(company, availability) }

    it_behaves_like 'a carrier dashboard controller action', :put

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_availability_url(company, availability, as: user.to_param) }

      context 'with valid params' do
        let(:attributes) { attributes_for(:carrier_availability) }

        it 'updates the requested carrier_availability' do
          patch url, params: { carrier_availability: attributes }
          expect(availability.reload).to have_attributes company:, **attributes
        end

        it 'redirects to the carrier_availability' do
          patch url, params: { carrier_availability: attributes }
          expect(response).to redirect_to carrier_dashboard_availabilities_url(company)
        end
      end

      context 'with invalid params' do
        let(:attributes) { attributes_for(:carrier_availability, origin_type: nil) }

        it 'does not update the requested carrier_availability' do
          patch url, params: { carrier_availability: attributes }
          availability.reload
          expect(availability.origin_type).not_to be_nil
        end

        it 'responds with unprocessable entity' do
          patch url, params: { carrier_availability: attributes }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'DELETE #destroy' do
    let!(:availability) { create :carrier_availability, company: }
    let(:url) { carrier_dashboard_availability_url(company, availability) }

    it_behaves_like 'a carrier dashboard controller action', :delete

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_availability_url(company, availability, as: user.to_param) }

      it 'destroys the requested carrier_availability' do
        expect do
          delete url
        end.to change(CarrierAvailability, :count).by(-1)
      end

      it 'redirects to the carrier_availabilities list' do
        delete url
        expect(response).to redirect_to carrier_dashboard_availabilities_url(company)
      end

      context 'when destroy fails' do
        before do
          allow_any_instance_of(CarrierAvailability).to receive(:destroy).and_return(false)
        end

        it 'responds with unprocessable status' do
          delete url
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end
end
