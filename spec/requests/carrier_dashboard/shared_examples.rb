RSpec.shared_examples 'a carrier dashboard controller action' do |method|
  context 'when user not logged in' do
    it 'redirects to login' do
      public_send method, url
      expect(response).to redirect_to sign_in_url
    end
  end

  context 'when user is logged in' do
    context 'when user is not a carrier' do
      let(:user) { create :user, :broker }

      it 'redirects back to root' do
        public_send method, Functions::AppendToUrl.call(url, as: user.to_param)
        expect(response).to redirect_to root_url
      end
    end

    context 'when carrier profile user is not verified' do
      let(:profile_user) { create :carrier_profile_user, :pending, carrier_profile: }

      it 'redirects to carrier claim page' do
        public_send method, Functions::AppendToUrl.call(url, as: user.to_param)
        expect(response).to redirect_to carrier_claim_url(company, profile_user)
      end
    end
  end
end
