require 'rails_helper'
require_relative '../shared_examples'
require_relative '../shared_contexts'

RSpec.describe CarrierDashboard::Analytics::IntegrationsController do
  include_context 'with verified carrier profile user'

  describe 'GET index' do
    let(:url) { carrier_dashboard_analytics_integrations_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_analytics_integrations_url(company, as: user.to_param) }

      before do
        create :analytics_integration, :carrier, :slack, company:
      end

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'POST create' do
    let(:url) { carrier_dashboard_analytics_integrations_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :post

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_analytics_integrations_url(company, as: user.to_param) }

      context 'with slack provider' do
        it 'redirects to the authentication URL' do
          post url, params: { analytics_integration: { provider: 'slack' } }
          expect(response).to redirect_to %r{https://slack.com/oauth/v2/authorize}
        end
      end

      context 'with hubspot provider' do
        it 'redirects to the authentication URL' do
          post url, params: { analytics_integration: { provider: 'hubspot' } }
          expect(response).to redirect_to %r{https://app.hubspot.com/oauth/authorize}
        end
      end

      context 'with salesforce provider' do
        it 'redirects to the authentication URL' do
          post url, params: { analytics_integration: { provider: 'salesforce' } }
          expect(response).to redirect_to %r{https://test.salesforce.com/services/oauth2/authorize}
        end
      end
    end
  end
end
