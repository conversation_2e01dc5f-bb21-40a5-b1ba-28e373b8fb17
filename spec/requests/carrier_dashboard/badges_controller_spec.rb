require 'rails_helper'
require_relative 'shared_examples'
require_relative 'shared_contexts'

RSpec.describe CarrierDashboard::BadgesController do
  include_context 'with verified carrier profile user'

  before do
    CarrierSource.redis.call('SADD', 'top-rated-carriers:2025', carrier.id.to_s)
  end

  describe 'GET #index' do
    let(:url) { carrier_dashboard_badges_url(carrier) }

    before do
      create :widget, :carrier, :badge, company:
    end

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_badges_url(carrier, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'GET #show' do
    let(:widget) { create :widget, :carrier, :badge, company: }
    let(:url) { carrier_dashboard_badge_url(carrier, widget) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_badge_url(carrier, widget, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH #update' do
    let(:widget) { create :widget, :carrier, :badge, company: }
    let(:url) { carrier_dashboard_badge_url(carrier, widget) }

    it_behaves_like 'a carrier dashboard controller action', :patch

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_badge_url(carrier, widget, as: user.to_param) }

      it 'updates the widget and redirects to edit page' do
        patch url, params: { widget: { enabled: 'true' } }
        expect(response).to redirect_to(carrier_dashboard_badge_url(carrier, widget))
      end

      context 'when the widget update fails' do
        before do
          allow_any_instance_of(Widget).to receive(:update).and_return(false)
        end

        it 'renders the show template' do
          patch url, params: { widget: { enabled: 'true' } }
          expect(response).to have_http_status :ok
        end
      end
    end
  end
end
