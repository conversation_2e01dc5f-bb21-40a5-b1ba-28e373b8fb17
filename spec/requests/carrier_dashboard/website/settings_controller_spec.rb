require 'rails_helper'
require_relative '../shared_examples'
require_relative '../shared_contexts'

RSpec.describe CarrierDashboard::Website::SettingsController do
  include_context 'with verified carrier profile user'

  describe 'GET show' do
    let(:url) { carrier_dashboard_website_settings_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_website_settings_url(company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { carrier_dashboard_website_settings_url(company) }
    let!(:carrier_profile_website) { create :carrier_profile_website, carrier_profile: }

    it_behaves_like 'a carrier dashboard controller action', :patch

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_website_settings_url(company, as: user.to_param) }

      it 'updates attributes' do
        patch url, params: { carrier_profile_website: { domain: 'www.tonytransport.com' } }
        expect(carrier_profile_website.reload.domain).to eq 'www.tonytransport.com'
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(CarrierProfileWebsite).to receive(:update).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url, params: { carrier_profile_website: { domain: 'www.tonytransport.com' } }
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end
end
