require 'rails_helper'
require_relative '../shared_examples'
require_relative '../shared_contexts'

RSpec.describe CarrierDashboard::Lanes::PreferredLanesController do
  include_context 'with verified carrier profile user'

  describe 'GET show' do
    let(:url) { carrier_dashboard_lanes_preferred_lanes_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :get

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_lanes_preferred_lanes_url(company, as: user.to_param) }

      it 'responds with ok' do
        get url
        expect(response).to have_http_status :ok
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { carrier_dashboard_lanes_preferred_lanes_url(company) }

    it_behaves_like 'a carrier dashboard controller action', :patch

    context 'when carrier user is verified' do
      let(:url) { carrier_dashboard_lanes_preferred_lanes_url(company, as: user.to_param) }
      let(:pickup) { create :city }
      let(:dropoff) { create :city }

      it 'updates attributes' do
        patch url, params: {
          carrier_profile: {
            preferred_lanes_attributes: { '0' => { pickup_city_id: pickup.id, dropoff_city_id: dropoff.id } }
          }
        }

        expect(carrier_profile.preferred_lanes.pluck(:pickup_city_id, :dropoff_city_id)).to eq [[pickup.id, dropoff.id]]
      end

      it 'queues up jobs' do
        patch url, params: {
          carrier_profile: {
            preferred_lanes_attributes: { '0' => { pickup_city_id: pickup.id, dropoff_city_id: dropoff.id } }
          }
        }

        expect(Elastic::SyncRecordJob.jobs.pick('args')).to eq [carrier.to_gid.to_s]
      end

      context 'when update fails' do
        before do
          allow_any_instance_of(CarrierProfile).to receive(:update).and_return(false)
        end

        it 'responds with unprocessable status' do
          patch url, params: {
            carrier_profile: {
              preferred_lanes_attributes: { '0' => { pickup_city_id: pickup.id, dropoff_city_id: dropoff.id } }
            }
          }

          expect(response).to have_http_status :unprocessable_content
        end

        it 'does not queue up elastic job' do
          patch url, params: {
            carrier_profile: {
              preferred_lanes_attributes: { '0' => { pickup_city_id: pickup.id, dropoff_city_id: dropoff.id } }
            }
          }

          expect(Elastic::SyncRecordJob.jobs.size).to eq 0
        end
      end
    end
  end
end
