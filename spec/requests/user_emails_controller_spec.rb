require 'rails_helper'

RSpec.describe UserEmailsController do
  let(:user) { create :user, :broker }

  describe 'POST create' do
    context 'when user is logged out' do
      it 'redirects to login' do
        post user_emails_url
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is logged in' do
      let(:url) { user_emails_url(as: user.to_param) }

      context 'with valid params' do
        it 'creates a new user email' do
          expect do
            post url, params: { user_email: { email: Faker::Internet.email } }
          end.to change(UserEmail, :count).by(1)
        end

        context 'with html format' do
          it 'redirects to dashboard profile' do
            post url, params: { user_email: { email: Faker::Internet.email } }
            expect(response).to redirect_to dashboard_profile_url
          end
        end

        context 'with turbo stream format' do
          it 'responds with turbo stream' do
            post url, params: { user_email: { email: Faker::Internet.email } }, as: :turbo_stream
            expect(response).to have_http_status :ok
          end
        end
      end

      context 'with invalid params' do
        it 'does not create a new user email' do
          expect do
            post url, params: { user_email: { email: nil } }
          end.not_to change(UserEmail, :count)
        end

        context 'with html format' do
          it 'redirects to dashboard profile' do
            post url, params: { user_email: { email: nil } }
            expect(response).to redirect_to dashboard_profile_url
          end
        end

        context 'with turbo stream format' do
          it 'responds with turbo stream' do
            post url, params: { user_email: { email: nil } }, as: :turbo_stream
            expect(response).to have_http_status :ok
          end
        end
      end
    end
  end

  describe 'DELETE destroy' do
    let!(:user_email) { create :user_email, user: }

    context 'when user is logged out' do
      it 'redirects to login' do
        delete user_email_url(user_email)
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is logged in' do
      let(:url) { user_email_url(user_email, as: user.to_param) }

      it 'deletes the user email' do
        expect { delete url }.to change(UserEmail, :count).by(-1)
      end

      context 'with html format' do
        it 'redirects to dashboard profile' do
          delete url
          expect(response).to redirect_to dashboard_profile_url
        end
      end

      context 'with turbo stream format' do
        it 'responds with turbo stream' do
          delete url, as: :turbo_stream
          expect(response).to have_http_status :ok
        end
      end
    end
  end

  describe 'GET verify' do
    let!(:user_email) { create :user_email, user: }

    it 'verifies the user email' do
      get verify_user_emails_url(token: user_email.verification_token)
      expect(user_email.reload).to be_verified
    end

    it 'redirects to dashboard profile' do
      get verify_user_emails_url(token: user_email.verification_token)
      expect(response).to redirect_to dashboard_profile_url
    end

    context 'when user email is already verified' do
      before do
        user_email.verify!
      end

      it 'does not verify the user email' do
        get verify_user_emails_url(token: user_email.verification_token)
        expect(user_email.reload).to be_verified
      end
    end
  end

  describe 'PATCH make_primary' do
    let(:url) { make_primary_user_email_url(user_email, as: user.to_param) }
    let!(:user_email) { create :user_email, :verified, user: }

    it 'makes the user email primary' do
      patch url
      expect(user_email.reload).to be_primary
    end

    context 'with html format' do
      it 'redirects to dashboard profile' do
        patch url
        expect(response).to redirect_to dashboard_profile_url
      end
    end

    context 'with turbo stream format' do
      it 'responds with turbo stream' do
        patch url, as: :turbo_stream
        expect(response).to have_http_status :ok
      end
    end
  end
end
