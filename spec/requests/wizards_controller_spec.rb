require 'rails_helper'

RSpec.describe WizardsController do
  describe 'GET show' do
    let(:company) { create :company, :carrier }

    context 'when request is coming from a bot' do
      it 'redirects to root' do
        get wizard_url(:claim_carrier, carrier_id: company.to_param), headers: { 'HTTP_USER_AGENT' => 'Googlebot' }
        expect(response).to redirect_to root_url
      end

      it 'does not store wizard in session' do
        get wizard_url(:claim_carrier, carrier_id: company.to_param), headers: { 'HTTP_USER_AGENT' => 'Googlebot' }
        expect(CarrierSource.redis.call('KEYS', 'wizard:*')).to be_empty
      end
    end

    context 'when request is not coming from a bot' do
      it 'redirects to start url' do
        get wizard_url(:claim_carrier, carrier_id: company.to_param)
        expect(response).to redirect_to sign_up_url
      end

      it 'stores wizard in session' do
        get wizard_url(:claim_carrier, carrier_id: company.to_param)
        expect(CarrierSource.redis.call('KEYS', 'wizard:*')).not_to be_empty
      end
    end
  end
end
