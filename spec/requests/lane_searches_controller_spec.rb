require 'rails_helper'

RSpec.describe LaneSearchesController, :elasticsearch do
  let(:user) { create :user, :broker }
  let(:origin) { create :city, name: 'Chicago', state_code: 'IL' }
  let(:destination) { create :city, name: 'Salt Lake City', state_code: 'UT' }

  describe 'GET index' do
    it 'returns http ok' do
      ClimateControl.modify LANE_SEARCH_CITY_IDS: "#{origin.id},#{destination.id}" do
        get lane_searches_url(as: user.to_param)
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'GET show' do
    let(:carrier_profile) { create :carrier_profile, company: }
    let(:company) { create :company, :carrier, :with_authority, :with_city, city: origin }

    before do
      create :preferred_lane, carrier_profile:, pickup_city: origin, dropoff_city: destination
      carrier_profile.operation_states.create!(state_id: 'united-states:utah')
      Carrier.es.import force: true, refresh: true
    end

    it 'returns http success' do
      get lane_search_url(origin: origin.full_slug, destination: destination.full_slug, as: user.to_param)
      expect(response).to have_http_status(:ok)
    end
  end

  describe 'POST create' do
    it 'returns http success' do
      post lane_searches_url(as: user.to_param), params: { origin: origin.id, destination: destination.id }
      expect(response).to redirect_to lane_search_url(origin: origin.full_slug, destination: destination.full_slug)
    end
  end
end
