require 'rails_helper'

RSpec.describe CarrierNetworkBuildersController do
  let(:user) { create :user, :shipper }
  let!(:carrier_network_builder) { create :carrier_network_builder, user: }

  shared_examples 'a protected action' do |method|
    context 'when user not logged in' do
      it 'redirects to login' do
        public_send method, url
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is logged in' do
      context 'when user is not an admin' do
        let(:user) { create :user, :shipper }

        it 'redirects back to root' do
          public_send method, Functions::AppendToUrl.call(url, as: user.to_param)
          expect(response).to redirect_to root_url
        end
      end
    end
  end

  describe 'GET show' do
    let(:url) { carrier_network_builder_path(carrier_network_builder.uuid) }

    it_behaves_like 'a protected action', :get

    context 'when user is an admin' do
      let(:user) { create :user, :shipper, admin: true }
      let(:url) { carrier_network_builder_path(carrier_network_builder.uuid, as: user.to_param) }

      it 'renders the show page' do
        get url
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe 'GET new' do
    let(:url) { new_carrier_network_builder_path }

    it_behaves_like 'a protected action', :get

    context 'when user is an admin' do
      let(:url) { new_carrier_network_builder_path(as: user.to_param) }
      let(:user) { create :user, :shipper, admin: true }

      it 'renders the new page' do
        get url
        expect(response).to have_http_status(:success)
      end
    end
  end

  describe 'POST create' do
    let(:url) { carrier_network_builders_path }

    it_behaves_like 'a protected action', :post

    context 'when user is an admin' do
      let(:user) { create :user, :shipper, admin: true }
      let(:url) { carrier_network_builders_path(as: user.to_param) }

      context 'when message is blank' do
        it 'redirects to new carrier network builder' do
          post url
          expect(response).to redirect_to new_carrier_network_builder_path
        end
      end

      context 'when message is present' do
        before do
          stub_request(:post, 'https://api.openai.com/v1/chat/completions')
            .and_return(status: 200, body:, headers: { 'Content-Type' => 'application/json' })
        end

        let(:content) do
          <<~JSON
            { "message": "Please specify your origin and destination" }
          JSON
        end

        let(:body) do
          {
            id: 'chatcmpl-123',
            object: 'chat.completion',
            created: 1_677_652_288,
            model: 'gpt-4o-mini',
            choices: [
              {
                index: 0,
                message: {
                  role: 'assistant',
                  content: content
                },
                finish_reason: 'stop'
              }
            ]
          }.to_json
        end

        it 'creates a new carrier network builder' do
          expect do
            post url, params: { message: 'Help me find a carrier network' }
          end.to change(CarrierNetworkBuilder, :count).by(1)
        end

        it 'redirects to the created carrier network builder' do
          post url, params: { message: 'Help me find a carrier network' }
          expect(response).to redirect_to carrier_network_builder_url(CarrierNetworkBuilder.last.uuid)
        end
      end
    end
  end

  describe 'GET edit' do
    let(:url) { edit_carrier_network_builder_path(carrier_network_builder.uuid) }

    it_behaves_like 'a protected action', :get

    context 'when user is an admin' do
      let(:user) { create :user, :shipper, admin: true }
      let(:url) { edit_carrier_network_builder_path(carrier_network_builder.uuid, as: user.to_param) }

      it 'renders the edit page' do
        get url
        expect(response).to have_http_status(:success)
      end
    end
  end

  describe 'PATCH update' do
    let(:url) { carrier_network_builder_path(carrier_network_builder.uuid) }
    let(:update_params) { { carrier_network_builder: { name: 'Updated Network' } } }

    it_behaves_like 'a protected action', :patch

    context 'when user is an admin' do
      let(:user) { create :user, :shipper, admin: true }
      let(:url) { carrier_network_builder_path(carrier_network_builder.uuid, as: user.to_param) }

      it 'updates the carrier network builder' do
        patch url, params: update_params
        expect(response).to redirect_to carrier_network_builder_path(carrier_network_builder.uuid)
        expect(carrier_network_builder.reload.name).to eq('Updated Network')
      end

      context 'when update fails' do
        let(:update_params) { { carrier_network_builder: { name: '' } } }

        it 'renders the edit page with errors' do
          patch url, params: update_params
          expect(response).to have_http_status :unprocessable_content
        end
      end
    end
  end

  describe 'DELETE destroy' do
    let(:url) { carrier_network_builder_path(carrier_network_builder.uuid) }

    it_behaves_like 'a protected action', :delete

    context 'when user is an admin' do
      let(:user) { create :user, :shipper, admin: true }
      let(:url) { carrier_network_builder_path(carrier_network_builder.uuid, as: user.to_param) }

      it 'deletes the carrier network builder' do
        expect { delete url }.to change(CarrierNetworkBuilder, :count).by(-1)
        expect(response).to redirect_to new_carrier_network_builder_url
      end
    end
  end

  describe 'POST chat' do
    let(:url) { chat_carrier_network_builder_path(carrier_network_builder.uuid) }

    it_behaves_like 'a protected action', :post

    context 'when user is an admin' do
      let(:user) { create :user, :shipper, admin: true }
      let(:url) { chat_carrier_network_builder_path(carrier_network_builder.uuid, as: user.to_param) }

      let(:content) do
        <<~JSON
          { "message": "Please specify your origin and destination" }
        JSON
      end

      let(:body) do
        {
          id: 'chatcmpl-123',
          object: 'chat.completion',
          created: 1_677_652_288,
          model: 'gpt-4o-mini',
          choices: [
            {
              index: 0,
              message: {
                role: 'assistant',
                content: content
              },
              finish_reason: 'stop'
            }
          ]
        }.to_json
      end

      before do
        stub_request(:post, 'https://api.openai.com/v1/chat/completions')
          .and_return(status: 200, body:, headers: { 'Content-Type' => 'application/json' })
      end

      context 'when message is blank' do
        it 'returns no content' do
          post url, params: { message: '' }
          expect(response).to have_http_status :no_content
        end
      end

      context 'when message is present' do
        before do
          create :carrier_network_builder_message, carrier_network_builder:
        end

        it 'processes the chat request' do
          post url, params: { message: 'Help me find a carrier network' },
                    headers: { 'Accept' => 'text/vnd.turbo-stream.html' }
          expect(response).to have_http_status :ok
        end

        context 'when uploading a csv file' do
          let(:file) { fixture_file_upload('blank.csv', 'text/csv') }

          it 'processes the file upload' do
            post url, params: { file: }, headers: { 'Accept' => 'text/vnd.turbo-stream.html' }
            expect(response).to have_http_status :ok
            expect(CarrierNetworkBuilderMessage.where(role: 'user').last.file).to be_present
          end
        end

        context 'when uploading an image file' do
          let(:file) { fixture_file_upload('logo.png', 'image/png') }

          it 'processes the image upload' do
            post url, params: { file: }, headers: { 'Accept' => 'text/vnd.turbo-stream.html' }
            expect(response).to have_http_status :ok
            expect(CarrierNetworkBuilderMessage.where(role: 'user').last.file).to be_present
          end
        end
      end
    end
  end

  describe 'POST export' do
    let(:url) { export_carrier_network_builder_path(carrier_network_builder.uuid) }

    it_behaves_like 'a protected action', :post

    context 'when user is an admin' do
      let(:user) { create :user, :shipper, admin: true }
      let(:url) { export_carrier_network_builder_path(carrier_network_builder.uuid, as: user.to_param) }
      let(:lane) { create :carrier_network_builder_lane, carrier_network_builder: }
      let(:company) { create :company, :carrier }

      before do
        CarrierNetworkBuilderLaneEntity.create!(carrier_network_builder_lane: lane, company:, entity_type: 'carrier')
      end

      context 'when type is csv' do
        it 'exports the carrier network builder' do
          post url, params: { type: 'csv' }
          expect(response).to have_http_status :ok
        end
      end

      context 'when type is xlsx' do
        it 'exports the carrier network builder' do
          post url, params: { type: 'xlsx' }
          expect(response).to have_http_status :ok
        end
      end
    end
  end
end
