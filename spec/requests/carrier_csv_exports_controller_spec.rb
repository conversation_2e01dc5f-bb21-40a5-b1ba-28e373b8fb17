require 'rails_helper'

RSpec.describe CarrierCsvExportsController do
  describe 'POST create', :elasticsearch do
    let!(:carrier_profile) { create :carrier_profile }
    let!(:brokerage_profile) { create :brokerage_profile }
    let(:url) { carrier_csv_export_url }

    before do
      Carrier.es.import force: true, refresh: true, query: -> { es_import_query }
      Brokerage.es.import force: true, refresh: true, query: -> { es_import_query }
    end

    context 'when user is not logged in' do
      it 'redirects to login page' do
        post url, params: { query: {}.to_json, type: 'csv' }
        expect(response).to redirect_to sign_in_url
      end
    end

    context 'when user is logged in' do
      let(:user) { create :user, :broker }
      let(:url) { carrier_csv_export_url(as: user.to_param) }

      context 'when user subscription does not exist' do
        it 'redirects to root' do
          post url, params: { query: {}.to_json, type: 'csv' }
          expect(response).to redirect_to root_url
        end
      end

      context 'when allotment is full' do
        before do
          create :access_package, resource: user
          AccessPackageAllotments::FindOrCreate.call(user:, name: 'carrier_csv_export').update(used: 2500)
        end

        it 'redirects to root' do
          post url, params: { query: {}.to_json, type: 'csv' }
          expect(response).to redirect_to root_url
        end
      end

      context 'when allotment has capacity' do
        before do
          create :access_package, :broker, resource: user
          AccessPackageAllotments::FindOrCreate.call(user:, name: 'carrier_csv_export')
        end

        it 'creates export file' do
          expect do
            post url, params: { query: {}.to_json, type: 'csv' }
          end.to change(CarrierCsvExport, :count).by(1)
        end

        context 'when type is xlsx' do
          it 'creates export file' do
            expect do
              post url, params: { query: {}.to_json, type: 'xlsx' }
            end.to change(CarrierCsvExport, :count).by(1)
          end
        end
      end
    end
  end
end
